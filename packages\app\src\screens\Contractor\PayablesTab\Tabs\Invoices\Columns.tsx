import React, { FC } from 'react'
import { StyleSheet, View } from 'react-native'
import { BtCheckBox } from '@linqpal/components/src/ui'
import {
  CellContainer,
  CheckBoxHeaderContainer,
} from '../../../../../ui/atoms/CellContainer'
import { PayableStatusLabel } from '../../../../../ui/molecules/StatusLabel/PayableStatusLabel'
import { ViewButton } from '../../Components/ViewButton'
import { paths } from '../../../../links'
import {
  invoiceStatus,
  PayablesInvoicesQuotesSort,
} from '@linqpal/models/src/dictionaries'
import {
  PaidInvoiceStatuses,
  PaymentProcessingInvoiceStatuses,
  UnviewableInvoiceStatuses,
} from '../../enums'
import { IInvoiceTableItem } from '../../Store/InvoicesStore'
import { TFunction } from 'react-i18next'
import { currencyMask } from '../../../../../utils/helpers/masking'
import { InvoicePaymentType } from '@linqpal/models'
import { InvoicePropertyGroup } from '../../../../../ui/molecules/InvoicePropertyGroup'
import { TooltipLabel } from '../../../../../ui/atoms/TooltipLabel'

type TNamespace = 'payables'

interface GetInvoiceColumnsProps {
  isFactoring: boolean
  vendorId: string | null
  hasPayableInvoice: boolean
  allSelected: boolean
  vendorsLength: number
  t: TFunction<TNamespace>
  navigation: any
  onSelectAll: () => void
  onSelect: (id: string) => void
}

export const getInvoiceColumns = ({
  isFactoring,
  vendorId,
  hasPayableInvoice,
  allSelected,
  vendorsLength,
  t,
  navigation,
  onSelectAll,
  onSelect,
}: GetInvoiceColumnsProps) => {
  const baseColumns = [
    ...(!isFactoring && vendorId && hasPayableInvoice
      ? [
          {
            name: (
              <CheckBoxHeaderContainer>
                <BtCheckBox checked={allSelected} onPress={onSelectAll} />
              </CheckBoxHeaderContainer>
            ),
            width: '40px',
            cell: (item: IInvoiceTableItem) => (
              <>
                {item.isPayable && (
                  <BtCheckBox
                    checked={item.selected}
                    onPress={() => onSelect(item._id)}
                  />
                )}
              </>
            ),
          },
        ]
      : []),

    ...(vendorsLength > 1 && !vendorId
      ? [
          {
            name: t('payables.tabInvoices.table.VendorName'),
            cell: (item: IInvoiceTableItem) => (
              <CellContainer value={item.supplierName} />
            ),
          },
        ]
      : []),

    {
      name: t('payables.tabInvoices.table.InvoiceNumber'),
      width: '138px',
      cell: (item: IInvoiceTableItem) => (
        <CellContainer value={item.invoiceNumber} />
      ),
    },
    {
      name: t('payables.tabInvoices.table.DueDate'),
      minWidth: '138px',
      cell: (item: IInvoiceTableItem) => item.dueDate,
      selector: PayablesInvoicesQuotesSort.DUE_DATE,
      sortable: true,
    },
    {
      name: t('payables.tabInvoices.table.Status'),
      minWidth: '198px',
      cell: (item: IInvoiceTableItem) => (
        <PayableStatusLabel status={item.status} />
      ),
    },
    {
      name: t('payables.tabInvoices.table.InvoiceAmount'),
      minWidth: '150px',
      cell: (item: IInvoiceTableItem) => currencyMask(item.totalAmount),
      selector: PayablesInvoicesQuotesSort.AMOUNT,
      sortable: true,
    },

    ...(isFactoring
      ? [
          {
            name: (
              <HeaderWithTooltip
                label={t('payables.tabInvoices.table.CustomerFee')}
                tooltip={t('payables.tabInvoices.table.CustomerFeeTooltip')}
              />
            ),
            minWidth: '125px',
            cell: (item: IInvoiceTableItem) => (
              <CellContainer value={item.customerFee} />
            ),
          },
        ]
      : []),

    {
      name: t('payables.tabInvoices.table.LateFee'),
      minWidth: '137px',
      cell: (item: IInvoiceTableItem) => <CellContainer value={item.lateFee} />,
    },

    ...(isFactoring
      ? [
          {
            name: (
              <HeaderWithTooltip
                label={t('payables.tabInvoices.table.TotalAmountDue')}
                tooltip={t('payables.tabInvoices.table.TotalAmountDueTooltip')}
              />
            ),
            minWidth: '150px',
            cell: (item: IInvoiceTableItem) => (
              <CellContainer value={item.totalAmountDue} />
            ),
          },
        ]
      : []),

    {
      name: t('payables.tabInvoices.table.AmountType'),
      minWidth: '138px',
      cell: (item: IInvoiceTableItem) => {
        const processingAmount =
          item.totalProcessingAmount &&
          item.paymentType === InvoicePaymentType.FACTORING
            ? item.totalProcessingAmount
            : PaymentProcessingInvoiceStatuses.includes(item.status) ||
              (item.status === invoiceStatus.due && item.payerId)
            ? item.totalAmount
            : 0

        const remainingAmount =
          PaymentProcessingInvoiceStatuses.includes(item.status) ||
          PaidInvoiceStatuses.includes(item.status)
            ? 0
            : item.totalRemainingAmount

        return (
          <InvoicePropertyGroup
            paidAmount={t('payables.tabInvoices.table.PaidAmount')}
            processingAmount={t('payables.tabInvoices.table.ProcessingAmount')}
            remainingAmount={t('payables.tabInvoices.table.RemainingAmount')}
            hasProcessingAmount={!!processingAmount}
            hasRemainingAmount={!!remainingAmount}
          />
        )
      },
    },

    {
      name: t('payables.tabInvoices.table.Amount'),
      width: '137px',
      cell: (item: IInvoiceTableItem) => {
        const paidAmount =
          item.totalPaidAmount &&
          item.paymentType === InvoicePaymentType.FACTORING
            ? item.totalPaidAmount
            : PaidInvoiceStatuses.includes(item.status)
            ? item.totalAmount
            : 0

        const processingAmount =
          item.paymentType === InvoicePaymentType.FACTORING
            ? item.totalProcessingAmount ?? 0
            : PaymentProcessingInvoiceStatuses.includes(item.status) ||
              (item.status === invoiceStatus.due && item.payerId)
            ? item.totalAmount
            : 0

        const remainingAmount =
          PaymentProcessingInvoiceStatuses.includes(item.status) ||
          PaidInvoiceStatuses.includes(item.status)
            ? 0
            : item.totalRemainingAmount

        return (
          <InvoicePropertyGroup
            paidAmount={currencyMask(paidAmount)}
            processingAmount={currencyMask(processingAmount)}
            remainingAmount={currencyMask(remainingAmount)}
            hasProcessingAmount={!!processingAmount}
            hasRemainingAmount={!!remainingAmount}
            tintedTextStyle={{ fontWeight: '700' }}
          />
        )
      },
    },

    {
      name: '',
      width: '100px',
      selector: (item: IInvoiceTableItem) => (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-end',
            marginTop: -10,
            height: '100%',
            position: 'absolute',
            alignItems: 'flex-start',
            right: 0,
            paddingRight: 8,
            width: 95,
          }}
        >
          {!UnviewableInvoiceStatuses.includes(item.status) &&
            !(
              item.totalProcessingAmount &&
              item.paymentType !== InvoicePaymentType.FACTORING
            ) && (
              <ViewButton
                onPress={() => {
                  if (item.paymentType === InvoicePaymentType.FACTORING) {
                    navigation.navigate(paths.Console.Payables.PaymentHistory, {
                      id: item._id,
                    })
                  } else if (
                    [invoiceStatus.creditApplied, invoiceStatus.paid].includes(
                      item.status,
                    )
                  ) {
                    navigation.navigate(paths.Console.Payables.Receipt, {
                      id: item._id,
                    })
                  } else {
                    navigation.navigate(paths.Console.Payables.Invoice, {
                      id: item._id,
                    })
                  }
                }}
              />
            )}
        </View>
      ),
    },
  ]

  return baseColumns
}

const HeaderWithTooltip: FC<{ label: string; tooltip: string }> = ({
  label,
  tooltip,
}) => {
  return (
    <View style={styles.headerWithTooltip}>
      <TooltipLabel
        label={label}
        tooltip={tooltip}
        labelStyle={styles.headerWithTooltipLabel}
        hoveredLabelStyle={styles.headerWithTooltipLabelHovered}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  headerWithTooltip: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    flexDirection: 'row',
    flexWrap: 'nowrap',
  },
  headerWithTooltipLabel: {
    fontSize: 14,
    lineHeight: 22,
    fontFamily: 'Inter, sans-serif',
    fontWeight: '700',
    backgroundColor: '#F8F9F9',
    color: '#19262F',
    display: 'flex',
    whiteSpace: 'nowrap',
  },
  headerWithTooltipLabelHovered: {
    color: 'rgba(0,0,0,.54)',
  },
})
