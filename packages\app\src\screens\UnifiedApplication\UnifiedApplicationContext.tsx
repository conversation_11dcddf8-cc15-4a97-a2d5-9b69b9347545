import React, { createContext, useContext, useState } from 'react'
import { UnifiedApplicationStore } from './store/UnifiedApplicationStore'

const UnifiedApplicationContext = createContext<UnifiedApplicationStore | null>(
  null,
)

export const useUnifiedApplication = () => {
  const context = useContext(UnifiedApplicationContext)

  if (!context) {
    throw new Error(
      'useUnifiedApplication must be used within a UnifiedApplicationProvider',
    )
  }

  return context
}

export const UnifiedApplicationProvider = ({ children }) => {
  const [store] = useState(() => new UnifiedApplicationStore())

  return (
    <UnifiedApplicationContext.Provider value={store}>
      {children}
    </UnifiedApplicationContext.Provider>
  )
}
