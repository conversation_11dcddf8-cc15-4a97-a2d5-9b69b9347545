import { IInvoiceDataFromJwt } from '@linqpal/models/src/dictionaries'
import { InvoicePaymentType } from '@linqpal/models'
import { ApiRequester } from '../helpers/ApiRequester'

interface IInvoicePaymentDetails {
  paymentType: InvoicePaymentType | null
  loanPlanId: string | null
  pricingPackageId: string | null
}

interface IInvoiceSupplierInvitationDetails {
  name: string
  email: string | null
  firstName: string | null
  lastName: string | null
  phone: string | null
  paymentMethodId: string | null
  userId: string | null
}

export enum DotNetInvoiceType {
  Invoice = 'Invoice',
  SalesOrder = 'SalesOrder',
  Quote = 'Quote',
}

export interface IDotNetInvoice {
  id: string
  total_amount: number
  invoice_number: string | null
  project_id: string | null
  type: DotNetInvoiceType
  paymentDetails: IInvoicePaymentDetails
  supplierInvitationDetails: IInvoiceSupplierInvitationDetails | null
  company_id: string | null
  customer_account_id: string | null
  payer_id: string | null
}

const getRequester = () => {
  const baseURL = process.env.NET_INVOICE_SERVICE_API_URL
  if (baseURL === undefined) {
    throw new Error(
      'NET_INVOICE_SERVICE_API_URL environment variable is not provided',
    )
  }
  return new ApiRequester(baseURL, 'Invoice', process.env.NET_INVOICE_APIKEY)
}

async function getInvoicesById(invoiceIds: string[]) {
  const requester = getRequester()

  const response = await requester.post<IDotNetInvoice[]>(
    `invoices/getByIds`,
    invoiceIds,
  )

  return response
}

async function checkAndGetInvoiceFromJwt(
  jwtToken: string,
): Promise<IInvoiceDataFromJwt> {
  const requester = getRequester()

  const response = await requester.post<IInvoiceDataFromJwt>(
    `invoices/jwt/health-check`,
    { jwtToken },
  )

  return response
}

async function upsertInvoiceFromJwt(jwtToken: string): Promise<string> {
  const requester = getRequester()

  const response = await requester.post<{ invoiceId: string }>(
    `invoices/jwt/upsert`,
    { jwtToken },
  )

  return response?.invoiceId
}

async function getInvoice(invoiceId: string) {
  const requester = getRequester()

  return requester.get<IDotNetInvoice>(`invoices/${invoiceId}`)
}

export const invoiceService = {
  getInvoicesById,
  checkAndGetInvoiceFromJwt,
  upsertInvoiceFromJwt,
  getInvoice,
}
