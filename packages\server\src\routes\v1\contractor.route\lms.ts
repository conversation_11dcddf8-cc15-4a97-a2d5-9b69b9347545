import {
  BankAccount,
  Company,
  getPlan,
  initializeRepayment,
  LMS,
  LoanApplication,
  Logger,
  VirtualCard,
} from '@linqpal/common-backend'
import { exceptions } from '@linqpal/models'
import transactional from '../../../services/transactional.service'
import { authRequired } from '../../../services/auth.service'
import {
  DECISION_STEPS,
  LOAN_APPLICATION_STATUS,
  PAYMENT_METHOD_TYPE,
} from '@linqpal/models/src/dictionaries'
import moment from 'moment'
import { ControllerItem } from 'src/routes/controllerItem'
import { ILoanApplication } from '@linqpal/common-backend/src/models/types'
import {
  getInstallments,
  ITimelineItem,
} from '@linqpal/common-backend/src/services/lms.service'
import { getCardFee } from '@linqpal/models/src/helpers/getCardFee'

export const loan: ControllerItem = {
  get: async (req, res) => {
    const { id } = req.query
    const loanInfo = await LMS.getLoanInfo(id as string)

    const loanApplication = await LoanApplication.findOne({ lms_id: id })
    const loanTemplate = loanInfo?.loanParameters.find((lp) => lp.isActive)
    const installments: ITimelineItem[] = await getInstallments(id as string)
    console.log('INSTALLMENTS')
    console.log(installments)
    console.log('_____________')
    console.log('LOAN INFO')
    console.log(loanInfo)

    const company = loanInfo?.companyId
      ? await Company.findOne({ id: loanInfo?.companyId })
      : null

    res.send({
      result: 'ok',
      loan: { ...loanInfo, loanTemplate, installments },
      loanApplication,
      company,
    })
  },
}

export const makePayment: ControllerItem = {
  middlewares: {
    pre: [authRequired(), ...transactional.pre],
    post: transactional.post,
  },
  put: async (req, res, next) => {
    const { loanApplicationId, amount, bankAccountId } = req.body
    const loanApplication = await LoanApplication.findById(
      loanApplicationId,
    ).session(req.session)

    if (!loanApplication)
      throw new exceptions.LogicalError('Loan application does not exist')
    if (loanApplication.company_id !== req.company!.id)
      throw new exceptions.LogicalError(
        'Loan application does not belong to company',
      )

    const company = await Company.findById(req.company!.id).populate(
      'bankAccounts',
    )

    if (!company) throw new exceptions.LogicalError('Company does not exist')
    if (!company.bankAccounts || !company.bankAccounts.length)
      throw new exceptions.LogicalError(
        'Company does not have any bank accounts',
      )

    const bankAccount = company.bankAccounts.find((e) => e.id === bankAccountId)

    await initializeRepayment(
      {
        application: loanApplication,
        amountDue: amount,
        paymentDate: new Date(),
        bankAccount,
        logger: new Logger({ module: 'LoanPro', subModule: 'makePayment' }),
      },
      req.session,
    )

    //await LMS.performPayment(loanApplication.lms_id, amount)

    next()
  },
}

export const loans: ControllerItem = {
  get: async (req, res) => {
    const pipeline = [
      {
        $addFields: {
          ids: {
            $cond: {
              if: { $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'] },
              then: '$invoiceDetails.invoiceId',
              else: ['$invoiceDetails.invoiceId'],
            },
          },
        },
      },
      {
        $addFields: { cardId: '$invoiceDetails.cardId' },
      },
      { $match: { company_id: req.company!._id.toString() } },
      {
        $match: {
          $or: [
            {
              status: {
                $in: [
                  LOAN_APPLICATION_STATUS.APPROVED,
                  LOAN_APPLICATION_STATUS.AUTHORIZED,
                  LOAN_APPLICATION_STATUS.CLOSED,
                  LOAN_APPLICATION_STATUS.EXPIRED,
                ],
              },
            },
            // Auto Trade Credit draws waiting for disbursement
            // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
            {
              $and: [
                { status: LOAN_APPLICATION_STATUS.PROCESSING },
                { lms_id: { $ne: null } },
                { 'metadata.repayment.autoTradeCreditEnabled': true },
                { 'progress.step': DECISION_STEPS.PENDING_MERCHANT_TRANSFER },
              ],
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'invoices',
          as: 'invoices',
          let: {
            invoices_id: '$ids',
          },
          pipeline: [
            { $addFields: { invoice_id: '$$invoices_id' } },
            { $unwind: '$invoice_id' },
            {
              $addFields: {
                id: {
                  $convert: {
                    input: '$invoice_id',
                    to: 'objectId',
                    onError: null,
                  },
                },
              },
            },
            { $match: { $expr: { $eq: ['$_id', '$id'] } } },
          ],
        },
      },
      { $set: { cardId: { $ifNull: ['$cardId', ''] } } },
      {
        $lookup: {
          from: 'virtualcards',
          localField: 'cardId',
          foreignField: 'cardId',
          as: 'cardInfo',
        },
      },
      {
        $unwind: {
          path: '$cardInfo',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $or: [
            { lms_id: { $ne: null } },
            { status: LOAN_APPLICATION_STATUS.AUTHORIZED }, // authorized quote apps don't have lms_id but still has to be displayed in loan list
            {
              'cardInfo.updatedAt': {
                $gt: moment().subtract(15, 'day').toDate(),
              },
            },
          ],
        },
      },
    ]

    const loanApplications = await LoanApplication.aggregate(pipeline)
    const loanList: { [key: string]: any }[] = []
    let loansWithPaymentsData: any[] = []

    const loanIds = loanApplications.map((e) => ({
      id: e.lms_id,
    }))
    if (loanIds.length) {
      loansWithPaymentsData = await LMS.getLoansByIds({ ids: loanIds })
    }
    const process = async (
      loanApplication: ILoanApplication & {
        invoices: any[]
        invoiceId: string
      },
    ) => {
      const {
        invoices,
        invoiceDetails,
        approvedAmount,
        company_id,
        createdAt,
        invoiceId,
        status,
        _id,
        lms_id,
        loanpro_id,
        metadata,
        draft,
      } = loanApplication

      const virtualCard = await VirtualCard.findOne({
        cardId: invoiceDetails?.cardId,
      })

      const validTill = moment(loanApplication.updatedAt, 'MM/DD/YY')
        .add(invoiceDetails?.cardId ? 3 : 7, 'days')
        .format('MM/DD/YY')
      const plan = await getPlan(invoiceDetails.paymentPlan)

      const loanData: { [key: string]: any } = {
        invoices,
        invoiceDetails,
        approvedAmount,
        company_id,
        createdAt,
        invoiceId,
        status,
        _id,
        lms_id,
        loanpro_id,
        option: plan,
        metadata,
        card: virtualCard
          ? {
              status: virtualCard?.status,
              cardId: virtualCard?.cardId,
              useDate: virtualCard?.useDate,
              usedAmount: virtualCard?.usedAmount,
              cardExpiryDate: validTill,
            }
          : null,
        draft,
      }

      loanData.lms = loansWithPaymentsData.find(
        (e) => e.id === loanApplication.lms_id,
      )

      if (invoices.length > 0) {
        const invoice = invoices[0]
        if (invoice.company_id)
          loanData.invoiceSenderCompany = await Company.findById(
            invoice.company_id,
            { _id: 1, name: 1 },
          )
      }
      loanList.push(loanData)
    }
    for (const i of loanApplications) {
      await process(i)
    }

    res.send({ result: 'ok', loans: loanList })
  },
}

export const paymentPlan: ControllerItem = {
  get: async (req, res) => {
    const { id } = req.query
    const r = await LMS.getPayments(id as string)
    res.send({ result: 'ok', list: r })
  },
}

export const loanPayableDetails: ControllerItem = {
  get: async (req, res) => {
    const { id } = req.query
    const r = await LMS.getLoanPayablesDetails(id as string, moment())
    res.send({ result: 'ok', data: r })
  },
}

export const calculateCardFees: ControllerItem = {
  get: async (req, res) => {
    const { paymentMethodId } = req.query
    const method = await BankAccount.findOne({ _id: paymentMethodId })
    let fees = 0
    if (method?.paymentMethodType === PAYMENT_METHOD_TYPE.CARD) {
      const { network, type, isRegulated } = method.cardMetadata
      const card = { cardNetwork: network, cardType: type, isRegulated }
      fees = getCardFee(card)
    }
    res.send({ result: 'ok', fees })
  },
}
