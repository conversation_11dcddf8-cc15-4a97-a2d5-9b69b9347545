/* eslint-disable prettier/prettier */
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Text, View } from 'react-native'
import { dictionaries, EInvoiceType } from '@linqpal/models'

import Loading from '../../../Loading'
import { Spacer } from '../../../../ui/atoms'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import Wrapper from '../../../Contractor/Wrapper'
import {
  BankAccountPaymentMethods,
  IntegrationErrorCodesType,
  invoiceStatus,
  LOAN_APPLICATION_STATUS,
  LoanApplicationStatuses,
} from '@linqpal/models/src/dictionaries'
import {
  APPROVED_STATUSES,
  PROCESSING_STATUSES,
} from '@linqpal/models/src/dictionaries'
import { ApplyBTCreditItem_v2 } from '../../InvoiceDetails/components/ApplyBTCreditItem'
import { AddNewPaymentMethodModal } from '../../InvoiceDetails/components/AddNewPaymentMethodModal'
import { ConnectBankModal } from '../../InvoiceDetails/components/ConnectBankModal'
import { AddNewBankAccountFlow } from '../../InvoiceDetails/flows/AddNewBankAccountFlow'
import { PayWithBluetapeCreditFlow } from '../../InvoiceDetails/flows/PayWithBluetapeCreditFlow'
import { PayWithBankAccountFlow } from '../../InvoiceDetails/flows/PayWithBankAccountFlow'
import { PayWithCardFlow } from '../../InvoiceDetails/flows/PayWithCreditCardFlow'
import { AddNewCreditCardFlow } from '../../InvoiceDetails/flows/AddNewCreditCardFlow'

import { useGetUserInfo } from '../../InvoiceDetails/hooks/useGetUserInfo'
import { useGetPaymentMethods } from '../../InvoiceDetails/hooks/useGetPaymentMethods'
import { useGetUserProjects } from '../../InvoiceDetails/hooks/useGetUserProjects'
import { useGetPaymentPlans } from '../hooks/useGetPaymentPlans'
import {
  OrDivider,
  PaymentMethodItem,
} from '../../../Contractor/InvoicesTab/Invoice/components'
import { AddNewButton } from '../../../../ui/add-payment-method-components'
import { useNavigation } from '@react-navigation/core'
import { ScreenWrapper } from '../../InvoiceDetails/components/ScreenWrapper'
import {
  GoToAppButton,
  LogOutButton,
} from '../../InvoiceDetails/components/TopBarButtons'
import { InvoicePaymentProcessingInfo } from '../../InvoiceDetails/components/InvoicePaymentProcessingInfo'
import { InvoiceIsPaidInfo } from '../../InvoiceDetails/components/InvoiceIsPaidInfo'
import { PAGE_MODE } from '../../InvoiceDetails/utils/pageMode'
import { EVENT_TYPE } from '../../InvoiceDetails/utils/eventTypes'
import { styles } from '../../InvoiceDetails/styles'
import { FLOWS } from '../../InvoiceDetails/utils/flows'
import { usePayWithBlueTapeCredit } from '../hooks/usePayWithBlueTapeCredit'
import { usePaymentFlow } from '../../InvoiceDetails/hooks/usePaymentFlows'
import {
  onPaymentFailure,
  onPaymentSuccess,
} from '../hooks/usePaymentStatusHandlers'
import { PaymentMethod } from '../../../Contractor/InvoicesTab/Invoice/InvoiceDetails'
import { ConnectorType } from '../../JwtIntegration/types'
import { NotEnoughPermissions } from '../../InvoiceDetails/flows/NotEnoughPermissions'
import { InvoiceStatusInfo } from '../../InvoiceDetails/components/InvoiceStatus'
import { useGetInvoice } from './hooks/useGetInvoice'
import { InvoicePanel } from '../components/InvoicePanel'
import { StatusLabel } from '../../../../ui/molecules/StatusLabel/StatusLabel'
import { getSalesDocumentStatusUIOptions } from '../../../../ui/molecules/StatusLabel/options/getSalesDocumentStatusUIOptions'
import { InvoicePanelMobile } from '../components/InvoicePanelMobile'
import { SyncInvoiceAgain } from '../../InvoiceDetails/components/SyncInvoiceAgain'

interface InvoiceDetailsProps {
  invoiceNumber: string
  supplierCompanyId: string
  minBnplAmount: number
  onLogOut: () => void
  onPermissionDenied: () => void
  isWeb: boolean
  mode?: PAGE_MODE
  redirectUrl?: string
}

export const InvoiceDetails: React.FC<InvoiceDetailsProps> = observer(
  ({
    invoiceNumber,
    supplierCompanyId,
    minBnplAmount = 5,
    onLogOut,
    onPermissionDenied,
    isWeb,
    mode,
    redirectUrl,
  }) => {
    window?.opener?.postMessage(
      { type: EVENT_TYPE.CONNECTION_ESTABLISHED },
      '*',
    )
    window?.parent?.postMessage(
      { type: EVENT_TYPE.CONNECTION_ESTABLISHED },
      '*',
    )
    const navigation = useNavigation() as any
    const { t } = useTranslation('global')
    const [flow, setFlow] = useState<FLOWS>(FLOWS.NONE)
    const [popupWindow, setPopupWindow] = useState<Window | null>(null)
    const [errorCode, setErrorCode] =
      useState<null | IntegrationErrorCodesType>(null)
    const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(
      null,
    )
    const expectedOrigin = window.location.origin

    let windowFeatures: string | undefined
    if (mode && mode === PAGE_MODE.POPUP)
      windowFeatures = 'width=800,height=600,scrollbars=yes,resizable=yes'

    const {
      invoice,
      invoiceMetadata,
      loading: getInvoiceLoading,
      isInvoicePaid,
      invoicePaymentStatus,
    } = useGetInvoice(invoiceNumber, supplierCompanyId, setErrorCode)

    const { userInfo, loading: getUserInfoLoading } =
      useGetUserInfo(setErrorCode)

    const {
      paymentMethods,
      refetch,
      loading: getPaymentMethodsLoading,
    } = useGetPaymentMethods(setErrorCode)
    const { userProjects, loading: getUserProjectLoading } =
      useGetUserProjects(setErrorCode)
    const { paymentPlans, loading: getPaymentPlansLoading } =
      useGetPaymentPlans(
        invoice?._id ?? '',
        Number(invoice?.total_amount),
        setErrorCode,
      )

    const loading =
      getInvoiceLoading ||
      getUserInfoLoading ||
      getPaymentMethodsLoading ||
      getUserProjectLoading ||
      getPaymentPlansLoading

    const {
      handleAddNewBankAccount,
      handleAddNewCreditCard,
      handleAddNewPaymentMethod,
      handlePayWithBankOrCard,
      handleResetFlow,
      handleNewPaymentMethodAdded,
      handlePrimaryForCreditChanged,
      handleLinkBankManually,
    } = usePaymentFlow(setFlow, setPaymentMethod, setErrorCode, refetch)

    const email = useMemo(
      () => userInfo?.user?.email || userInfo?.user?.login || '',
      [userInfo],
    )

    useEffect(() => {
      if (invoice?.connector?.connector_type === ConnectorType.NetSuite) {
        if (
          invoice.customerCompanyId &&
          invoice.customerCompanyId !== userInfo?.company?._id &&
          invoice?.company_id !== userInfo?.company?._id
        ) {
          setFlow(FLOWS.NOT_ENOUGH_PERMISSIONS)
        }
      }
    }, [invoice, userInfo])

    const handlePayWithBlueTapeCredit = usePayWithBlueTapeCredit(
      userInfo,
      paymentMethods,
      setPopupWindow,
      setFlow,
      setPaymentMethod,
      windowFeatures,
    )

    const filteredPaymentMethods = useMemo(() => {
      if (!paymentMethods || !invoiceMetadata) {
        return []
      }

      return paymentMethods.filter((acc: any) => {
        if (
          acc.paymentMethodType === BankAccountPaymentMethods.Bank &&
          !invoiceMetadata.acceptAchPayment
        ) {
          return false
        }
        if (
          acc.paymentMethodType !== BankAccountPaymentMethods.Bank &&
          !invoiceMetadata.cardPaymentAvailable
        ) {
          return false
        }
        return true
      })
    }, [paymentMethods, invoiceMetadata])

    const handlePaymentSuccess = useCallback(() => {
      console.log('paymentSuccess', email)
      onPaymentSuccess({ email, navigation, mode, redirectUrl })
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [email, mode, redirectUrl])

    const handlePaymentFailure = useCallback(() => {
      console.log('paymentFail')
      onPaymentFailure({ navigation, mode, redirectUrl })
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [mode, redirectUrl])

    useEffect(() => {
      const handleMessage = (event: any) => {
        if (event.origin === expectedOrigin) {
          if (
            event.data.type === EVENT_TYPE.CREDIT_APPLICATION_SUBMITTED &&
            popupWindow
          ) {
            popupWindow.close()
            setPopupWindow(null)
            window.location.reload()
          }
        } else {
          console.warn('Received message from unexpected origin:', event.origin)
        }
      }
      window.addEventListener('message', handleMessage)
      return () => {
        window.removeEventListener('message', handleMessage)
      }
    }, [popupWindow, expectedOrigin])

    if (loading) {
      return <Loading />
    }

    if (!invoice) {
      return <SyncInvoiceAgain isWeb={isWeb} />
    }

    if (!invoiceMetadata || errorCode) return <></>

    if (flow === FLOWS.NOT_ENOUGH_PERMISSIONS) {
      return (
        <NotEnoughPermissions
          email={email}
          isWeb={isWeb}
          onPress={onPermissionDenied}
        />
      )
    }

    const isUserInvoiceSupplier = userInfo?.company?._id === invoice?.company_id

    const isQuote = invoice.type === EInvoiceType.QUOTE

    const isAmountLessThanMin = Number(invoice.total_amount) < minBnplAmount
    const isInvoiceDraft = invoice.status === invoiceStatus.draft

    const isInvoiceEligible =
      invoiceMetadata.bnplPaymentAvailable &&
      invoiceMetadata.currentInvoiceCreditStatus !==
        LOAN_APPLICATION_STATUS.REJECTED &&
      invoiceMetadata.loanApplicationStatus !==
        LOAN_APPLICATION_STATUS.REJECTED &&
      !!paymentPlans.length

    const invoiceCreditStatus = invoiceMetadata.currentInvoiceCreditStatus

    const isInvoiceCreditExist = [
      ...PROCESSING_STATUSES,
      ...APPROVED_STATUSES,
    ].includes(invoiceCreditStatus as LoanApplicationStatuses)

    /*const canPayIfQuoteType = isQuote
      ? invoice.connector?.connector_type === ConnectorType.NetSuite
      : true*/

    const canPay =
      !isUserInvoiceSupplier && !isInvoicePaid && !isInvoiceCreditExist
    //&& canPayIfQuoteType

    const canAddNewPaymentMethod =
      invoiceMetadata.acceptAchPayment || invoiceMetadata.cardPaymentAvailable

    const shouldShowChooseMethod =
      (canAddNewPaymentMethod ||
        isInvoiceEligible ||
        invoice?.isPaymentMethodsAdded) &&
      canPay

    return (
      <ScreenWrapper
        email={email}
        isWeb={isWeb}
        button={
          mode ? (
            <LogOutButton onLogOut={onLogOut} />
          ) : (
            <GoToAppButton isWeb={isWeb} mode={mode} />
          )
        }
      >
        {isWeb && (
          <View
            style={{
              width: 486,
              alignSelf: 'center',
              marginBottom: 60,
            }}
          >
            <InvoicePanel
              invoice={invoice}
              additionalFields={
                isUserInvoiceSupplier && !isInvoicePaid && !isInvoiceCreditExist
                  ? [
                      {
                        label: t('integrations.invoice.status'),
                        value: (
                          <StatusLabel
                            status={invoice.status ?? invoiceStatus.placed}
                            uiOptionsProvider={getSalesDocumentStatusUIOptions}
                          />
                        ),
                      },
                    ]
                  : []
              }
              // eslint-disable-next-line i18next/no-literal-string
              backgroundColor={'#F8F9F9'}
            />
          </View>
        )}
        <Wrapper
          containerStyle={{ alignSelf: 'center' }}
          contentContainerStyle={{ height: '100%' }}
        >
          {!isWeb && <InvoicePanelMobile invoice={invoice} />}
          {!isWeb &&
            isUserInvoiceSupplier &&
            !isInvoicePaid &&
            !isInvoiceCreditExist && (
              <InvoiceStatusInfo
                status={invoice.status || invoiceStatus.placed}
              />
            )}
          {((isInvoicePaid &&
            invoicePaymentStatus === dictionaries.OPERATION_STATUS.SUCCESS) ||
            invoiceCreditStatus === LOAN_APPLICATION_STATUS.CLOSED) && (
            <InvoiceIsPaidInfo isWeb={isWeb} />
          )}
          {((isInvoiceCreditExist &&
            invoiceCreditStatus !== LOAN_APPLICATION_STATUS.CLOSED) ||
            (isInvoicePaid &&
              invoicePaymentStatus ===
                dictionaries.OPERATION_STATUS.PROCESSING)) && (
            <InvoicePaymentProcessingInfo isWeb={isWeb} />
          )}

          {shouldShowChooseMethod && (
            <Text
              style={styles.chooseMethodText}
              testID={'choose-payment-method-title'}
            >
              {t('InvoiceDetails.choose-method')}
            </Text>
          )}

          {isInvoiceEligible && canPay && (
            <>
              <ApplyBTCreditItem_v2
                currentInvoiceCreditStatus={
                  invoiceMetadata.currentInvoiceCreditStatus
                }
                amountLessThanMin={Number(invoice.total_amount) < minBnplAmount}
                onPress={handlePayWithBlueTapeCredit}
                showInfo
                disabled={
                  isInvoiceCreditExist || isAmountLessThanMin || isInvoiceDraft
                }
              />
              <Spacer height={23} />
              {!(
                isInvoiceCreditExist ||
                isAmountLessThanMin ||
                isInvoiceDraft ||
                isQuote
              ) && <OrDivider />}
            </>
          )}

          {canPay && canAddNewPaymentMethod && !isQuote && (
            <>
              {filteredPaymentMethods.map((acc, i) => (
                <PaymentMethodItem
                  key={i}
                  item={acc}
                  disabled={isInvoiceDraft}
                  onPaymentSelection={handlePayWithBankOrCard}
                />
              ))}

              <AddNewButton
                onPress={handleAddNewPaymentMethod}
                disabled={isInvoiceDraft}
              />

              <Spacer height={43} />
            </>
          )}

          {flow === FLOWS.ADD_NEW_PAYMENT_METHOD && (
            <AddNewPaymentMethodModal
              cardPaymentAvailable={invoiceMetadata.cardPaymentAvailable}
              acceptAchPayment={invoiceMetadata.acceptAchPayment}
              onLinkCard={handleAddNewCreditCard}
              onConnectBank={handleAddNewBankAccount}
              onClose={handleResetFlow}
            />
          )}
          {flow === FLOWS.ADD_NEW_BANK_ACOUNT && (
            <AddNewBankAccountFlow
              onClose={handleResetFlow}
              onSuccess={handleNewPaymentMethodAdded}
              onLinkBankAccountManually={handleLinkBankManually}
            />
          )}
          {flow === FLOWS.ADD_NEW_CREDIT_CARD && (
            <AddNewCreditCardFlow
              onClose={handleResetFlow}
              onSuccess={handleNewPaymentMethodAdded}
              invoiceId={invoice._id}
            />
          )}
          {flow === FLOWS.PAY_WITH_BLUETAPE_CREDIT_FLOW &&
            paymentMethod &&
            invoice &&
            userProjects &&
            userInfo &&
            paymentPlans && (
              <PayWithBluetapeCreditFlow
                invoiceId={invoice._id}
                totalAmount={Number(invoice.total_amount)}
                companyName={invoice.company.name}
                paymentMethodName={paymentMethod.name}
                onClose={handleResetFlow}
                onSuccess={handlePaymentSuccess}
                onFail={handlePaymentFailure}
                userProjects={userProjects}
                paymentPlans={paymentPlans}
                invoice_connector_project={invoice.connector?.project}
                invoice_connector_integration_id={
                  invoice.connector?.integration_id
                }
                company_credit_purchaseType={
                  userInfo.company?.credit?.purchaseType
                }
                // document_loanApplicationId={userInfo.do}
              />
            )}
          {flow === FLOWS.PAY_WITH_BANK_ACCOUNT_FLOW &&
            paymentMethod &&
            invoice && (
              <PayWithBankAccountFlow
                invoiceId={invoice._id}
                invoiceCompanyName={invoice.company.name}
                paymentMethod={paymentMethod}
                onClose={handleResetFlow}
                onSuccess={handlePaymentSuccess}
                onFail={handlePaymentFailure}
              />
            )}
          {flow === FLOWS.PAY_WITH_CARD_FLOW && paymentMethod && invoice && (
            <PayWithCardFlow
              invoiceId={invoice._id}
              invoiceCompanyName={invoice.company.name}
              paymentMethod={paymentMethod}
              onClose={handleResetFlow}
              onSuccess={handlePaymentSuccess}
              onFail={handlePaymentFailure}
            />
          )}
          {flow === FLOWS.CONNECT_NEW_BANK_ACCOUNT && (
            <ConnectBankModal
              onClose={handleResetFlow}
              onPrimaryForCreditChanged={handlePrimaryForCreditChanged}
            />
          )}
        </Wrapper>
      </ScreenWrapper>
    )
  },
)
