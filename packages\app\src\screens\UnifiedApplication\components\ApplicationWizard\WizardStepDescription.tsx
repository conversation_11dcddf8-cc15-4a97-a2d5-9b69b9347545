import { BtPlainText } from '@linqpal/components/src/ui'
import React, { FC } from 'react'
import { StyleSheet } from 'react-native'
import { IApplicationStepDescription } from '../../flow/ApplicationEditorSelector'
import { useTranslation } from 'react-i18next'
import { useResponsive } from '../../../../utils/hooks'

interface IProps {
  description?: IApplicationStepDescription
}

export const WizardStepDescription: FC<IProps> = ({ description }) => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  if (!description) return null

  return (
    <BtPlainText
      style={[
        styles.description,
        sm ? styles.descriptionDesktop : styles.descriptionMobile,
        description.style,
      ]}
    >
      {t(description.text as any)}
    </BtPlainText>
  )
}

const styles = StyleSheet.create({
  description: {
    fontSize: 16,
    fontWeight: '600',
    color: '#335C75',
  },
  descriptionDesktop: {
    lineHeight: 30,
    marginBottom: 24,
  },
  descriptionMobile: {
    marginBottom: 12,
  },
})
