import numbro from 'numbro'

export function toCurrency(amount: number | null | undefined): string {
  return numbro(amount ?? 0).formatCurrency('0,0.00')
}

export function toPercentage(
  amount: number | null | undefined,
  type: 'fraction' | 'whole' = 'whole',
): string {
  // use fraction for 0.05 (5%)
  // use whole for 5 (5%)
  const percentage = type === 'whole' ? (amount ?? 0) / 100 : amount ?? 0

  return numbro(percentage).format({
    output: 'percent',
    mantissa: 2,
    trimMantissa: true,
  })
}
