import { ICompany, IInvoice, ILoanApplication } from '../../models/types'
import mongoose, { ClientSession } from 'mongoose'
import { Company, CustomerAccount } from '../../models'
import { getOwningUser } from '../company.service'
import { getLoginUrl } from '../branding.service'
import SmsNotifications from '../../helpers/SmsNotifications'
import EmailNotifications, {
  EmailNotification,
} from '../../helpers/EmailNotifications'
import { CompanyUtils } from '@linqpal/models/src/helpers/companyUtils'
import smsService from '../sms.service'
import { emailService } from '../email.service'
import { Logger } from '../logger/logger.service'
import { findBuilderByAccountId } from '../invoices.service/findBuilderAccount'
import { getInvoiceLink } from '../invoices.service/notifications'
import { getApiURL, LMS } from '../../../index'
import moment from 'moment'
import { WebNotificationsService } from '../webNotificationsService'
import { INotificationModel, InvoicePaymentType } from '@linqpal/models'
import {
  alertTypes,
  NotificationTypes,
  notificationTypes,
} from '@linqpal/models/src/dictionaries'
import { toCurrency } from '@linqpal/models/src/helpers/formatter'
import { CriticalError } from '@linqpal/models/src/types/exceptions'
import { ActionPerformer } from '@linqpal/models/src/dictionaries/actionPerformer'
import {
  AutomatedApprovalResult,
  AutomatedDecisionResult,
  IDrawApproval,
} from '../onBoarding/types'
import { QuoteService } from './quote.service'

export class QuoteNotifications {
  private static logger = new Logger({
    module: 'bluetape.services',
    subModule: 'QuoteService',
  })

  public static async quoteCreated(
    quote: IInvoice,
    session: ClientSession | null,
  ): Promise<void> {
    const [customer, supplier] = await Promise.all([
      findBuilderByAccountId(quote.customer_account_id),
      Company.findById(quote.company_id),
    ])

    if (!supplier) throw new Error(`no supplier for ${quote.id}`)

    if (!customer) {
      return this.quoteCreateToInvitedUser(quote, supplier)
    }

    const [customerUser, loginUrl] = await Promise.all([
      getOwningUser(customer.id),
      getLoginUrl(customer.id),
    ])

    if (!customerUser) throw new Error(`no customer user for ${quote.id}`)

    const quoteLink = await getInvoiceLink(quote.id, customer, loginUrl)
    const supplierCompanyName = CompanyUtils.getCompanyName(supplier)

    const webNotification = this.createWebNotification(
      quote,
      supplier.id,
      customer.id,
      notificationTypes.quotePlaced,
      // prettier-ignore
      `${supplierCompanyName} has sent you quote for ${toCurrency(quote.total_amount)}`,
    )

    const smsNotification = SmsNotifications.quoteCreated({
      supplierName: supplierCompanyName,
      quoteAmount: quote.total_amount,
      quoteLink,
    })

    await Promise.all([
      smsService.send(customerUser.phone, smsNotification),

      emailService.sendToCompanyUsersPersonalized(customer.id, (firstName) =>
        EmailNotifications.quoteCreated({
          customerName: firstName,
          supplierName: supplierCompanyName,
          quoteAmount: quote.total_amount,
          quoteLink,
        }),
      ),

      WebNotificationsService.send(webNotification, session),
    ])
  }

  public static async quoteDrawCreated(
    app: ILoanApplication,
    invoice: IInvoice,
  ): Promise<void> {
    const [supplier, customer, customerUser, loan, loginUrl] =
      await Promise.all([
        Company.findById(invoice.company_id),
        Company.findById(app.company_id),
        getOwningUser(app.company_id),
        LMS.getLoanInfo(app.lms_id),
        getLoginUrl(app.company_id),
      ])

    if (!loan) throw new Error(`no loan for app ${app.id}`)
    if (!customer) throw new Error(`no customer user for app ${app.id}`)
    if (!supplier) throw new Error(`no supplier user for app ${app.id}`)
    if (!customerUser) throw new Error(`no customer user for app ${app.id}`)

    const smsMessage = SmsNotifications.quoteDrawCreated({
      supplierName: supplier.name,
      drawAmount: loan.loanDetails?.totalLoanAmount ?? 0,
      invoiceNumber: invoice.invoice_number,
      loginUrl,
    })

    const supplierName = CompanyUtils.getCompanyName(supplier)

    await Promise.allSettled([
      await smsService.send(customerUser.phone, smsMessage),

      await emailService.sendToCompanyUsersPersonalized(
        app.company_id,
        (firstName) =>
          EmailNotifications.quoteDrawCreated({
            customerName: firstName,
            supplierName: supplierName,
            invoiceAmount: invoice.total_amount,
            invoiceNumber: invoice.invoice_number,
            drawAmount: loan.loanDetails?.totalLoanAmount ?? 0,
            repaymentDate: loan.loanDetails?.nextPaymentDate
              ? moment(loan.loanDetails?.nextPaymentDate)
              : null,
            loginUrl,
          }),
      ),
    ])
  }

  public static async quoteAuthorized(
    quote: IInvoice,
    drawApproval: IDrawApproval,
    session: ClientSession | null,
  ): Promise<void> {
    // prettier-ignore
    if (!drawApproval.companyId) throw new CriticalError('no customer company', { drawApproval })

    const [customer, supplier, customerUser, expirationSeconds] =
      await Promise.all([
        Company.findById(drawApproval.companyId),
        Company.findById(quote.company_id),
        getOwningUser(drawApproval.companyId),
        QuoteService.getExpirationSeconds(),
      ])

    // prettier-ignore
    {
      if (!customer) throw new Error(`no customer for ${quote.id}`)
      if (!supplier) throw new Error(`no supplier for quote ${quote.id}`)
      if (!customerUser) throw new Error(`no customer user for quote ${quote.id}`)
    }

    if (!quote.quoteDetails?.authorization_deadline) {
      // prettier-ignore
      throw new CriticalError('no authorization deadline in authorized quote', { quote })
    }

    const expirationDays = Math.round(expirationSeconds / (60 * 60 * 24))

    const supplierCompanyName = CompanyUtils.getCompanyName(supplier)
    const customerCompanyName = CompanyUtils.getCompanyName(customer)

    if (quote.paymentDetails?.paymentType === InvoicePaymentType.FACTORING) {
      this.logger.info('sending factoring quote approved notifications')

      // no customer notifications for IHC quotes
      return emailService.sendToCompanyUsersPersonalized(
        quote.company_id,
        (firstName) =>
          EmailNotifications.quoteAuthorizedToSupplier(
            InvoicePaymentType.FACTORING,
            {
              supplierName: firstName,
              customerName: customerCompanyName,
              quoteNumber: quote.invoice_number,
              expirationDays,
            },
          ),
      )
    }

    const smsMessage = SmsNotifications.quoteApproved({
      supplierName: supplierCompanyName,
      quoteAmount: quote.total_amount,
      expirationDays,
    })

    const webNotification = this.createWebNotification(
      quote,
      supplier.id,
      customer.id,
      notificationTypes.quoteAuthorized,
      // prettier-ignore
      `${quote.invoice_number} was authorized for ${toCurrency(quote.total_amount)}`,
    )

    let opsTeamNotification: EmailNotification | null = null

    // prettier-ignore
    if (
      drawApproval?.automatedApprovalResult === AutomatedApprovalResult.Passed &&
      drawApproval?.automatedDecisionResult === AutomatedDecisionResult.Pass
    ) {
      opsTeamNotification = EmailNotifications.quoteAuthorizedToOpsTeam({
        supplier: supplierCompanyName,
        customer: customerCompanyName,
        quoteNumber: quote.invoice_number,
        quoteAmount: quote.total_amount,
        quoteExpiration: quote.quoteDetails.authorization_deadline,
      })
    }

    this.logger.info('sending quote approved notifications')

    await Promise.allSettled([
      customerUser.phone
        ? smsService.send(customerUser.phone, smsMessage)
        : Promise.resolve(),

      emailService.sendToCompanyUsersPersonalized(
        drawApproval.companyId,
        (firstName) =>
          EmailNotifications.quoteAuthorized({
            customerName: firstName,
            supplierName: supplierCompanyName,
            quoteNumber: quote.invoice_number,
            quoteAmount: quote.total_amount,
            drawAmount: quote.total_amount, // include fees etc. on phase #2?
            expirationDays,
          }),
      ),

      emailService.sendToCompanyUsersPersonalized(
        quote.company_id,
        (firstName) =>
          EmailNotifications.quoteAuthorizedToSupplier(
            quote.paymentDetails?.paymentType ?? null,
            {
              supplierName: firstName,
              customerName: customerCompanyName,
              quoteNumber: quote.invoice_number,
              expirationDays,
            },
          ),
      ),

      opsTeamNotification
        ? emailService.sendToOperations(opsTeamNotification)
        : Promise.resolve(),

      WebNotificationsService.send(webNotification, session),
    ])
  }

  public static async quoteInvoiceAssigned(
    quote: IInvoice,
    invoice: IInvoice,
    customerCompanyId: string,
    session: ClientSession | null,
  ): Promise<void> {
    const webNotification = this.createWebNotification(
      invoice,
      invoice.company_id, // supplier
      customerCompanyId, // customer
      notificationTypes.quoteInvoiced,
      `Quote ${quote.invoice_number} is invoiced`,
    )

    await WebNotificationsService.send(webNotification, session)
  }

  public static async quoteCanceled(
    quote: IInvoice,
    customerCompanyId: string,
    session: ClientSession | null,
  ): Promise<void> {
    const [customer, supplier, customerUser] = await Promise.all([
      Company.findById(customerCompanyId),
      Company.findById(quote.company_id),
      getOwningUser(customerCompanyId),
    ])

    if (!customer) throw new Error(`no customer for quote ${quote.id}`)
    if (!supplier) throw new Error(`no supplier user for quote ${quote.id}`)
    if (!customerUser) throw new Error(`no customer user for quote ${quote.id}`)

    const customerCompanyName = CompanyUtils.getCompanyName(customer)
    const supplierCompanyName = CompanyUtils.getCompanyName(supplier)

    if (quote.paymentDetails?.paymentType === InvoicePaymentType.FACTORING) {
      // no customer notifications for IHC quotes
      return emailService.sendToCompanyUsersPersonalized(
        quote.company_id,
        (firstName) =>
          EmailNotifications.quoteCanceledToSupplier(
            InvoicePaymentType.FACTORING,
            {
              customerName: customerCompanyName,
              supplierName: firstName,
              quoteNumber: quote.invoice_number,
            },
          ),
      )
    }

    const smsMessage = SmsNotifications.quoteCanceled({
      supplierName: supplierCompanyName,
      quoteNumber: quote.invoice_number,
    })

    const webNotification = this.createWebNotification(
      quote,
      supplier.id,
      customer.id,
      notificationTypes.quoteCanceled,
      `${quote.invoice_number} is cancelled`,
    )

    await Promise.allSettled([
      smsService.send(customerUser.phone, smsMessage),

      emailService.sendToCompanyUsersPersonalized(
        customerCompanyId,
        (firstName) =>
          EmailNotifications.quoteCanceled({
            customerName: firstName,
            supplierName: supplierCompanyName,
            quoteNumber: quote.invoice_number,
          }),
      ),

      emailService.sendToCompanyUsersPersonalized(
        quote.company_id,
        (firstName) =>
          EmailNotifications.quoteCanceledToSupplier(
            quote.paymentDetails?.paymentType ?? null,
            {
              customerName: customerCompanyName,
              supplierName: firstName,
              quoteNumber: quote.invoice_number,
            },
          ),
      ),

      WebNotificationsService.send(webNotification, session),
    ])
  }

  public static async quoteRejected(
    quote: IInvoice,
    customerCompanyId: string,
    session: ClientSession | null,
  ): Promise<void> {
    const [customer, supplier, customerUser, loginUrl] = await Promise.all([
      Company.findById(customerCompanyId),
      Company.findById(quote.company_id),
      getOwningUser(customerCompanyId),
      getLoginUrl(customerCompanyId),
    ])

    if (!customer) throw new Error(`no customer for quote ${quote.id}`)
    if (!supplier) throw new Error(`no supplier user for quote ${quote.id}`)
    if (!customerUser) throw new Error(`no customer user for quote ${quote.id}`)

    const customerCompanyName = CompanyUtils.getCompanyName(customer)
    const supplierCompanyName = CompanyUtils.getCompanyName(supplier)

    if (quote.paymentDetails?.paymentType === InvoicePaymentType.FACTORING) {
      // no customer notifications for IHC quotes
      return emailService.sendToCompanyUsersPersonalized(
        quote.company_id,
        (firstName) =>
          EmailNotifications.quoteRejectedToSupplier(
            InvoicePaymentType.FACTORING,
            {
              customerName: customerCompanyName,
              supplierName: firstName,
              quoteNumber: quote.invoice_number,
            },
          ),
      )
    }

    const smsMessage = SmsNotifications.quoteRejected({
      supplierName: supplierCompanyName,
      quoteNumber: quote.invoice_number,
      loginUrl,
    })

    const webNotification = this.createWebNotification(
      quote,
      supplier.id,
      customer.id,
      notificationTypes.quoteRejected,
      `${quote.invoice_number} is rejected, pay with ACH or Card`,
    )

    await Promise.allSettled([
      smsService.send(customerUser.phone, smsMessage),

      emailService.sendToCompanyUsersPersonalized(
        customerCompanyId,
        (firstName) =>
          EmailNotifications.quoteRejected({
            customerName: firstName,
            supplierName: supplierCompanyName,
            quoteNumber: quote.invoice_number,
          }),
      ),

      emailService.sendToCompanyUsersPersonalized(
        quote.company_id,
        (firstName) =>
          EmailNotifications.quoteRejectedToSupplier(
            quote.paymentDetails?.paymentType ?? null,
            {
              customerName: customerCompanyName,
              supplierName: firstName,
              quoteNumber: quote.invoice_number,
            },
          ),
      ),

      WebNotificationsService.send(webNotification, session),
    ])
  }

  public static async authorizationExtended(
    quote: IInvoice,
    actionPerformer: ActionPerformer,
  ): Promise<void> {
    const customer = await findBuilderByAccountId(quote.customer_account_id)
    if (!customer) throw new Error(`no customer for quote ${quote.id}`)

    const [customerUser, supplier] = await Promise.all([
      getOwningUser(customer.id),
      Company.findById(quote.company_id),
    ])

    // prettier-ignore
    if (!quote.quoteDetails?.authorization_deadline) throw new Error(`no authorization deadline for quote ${quote.id}`)
    if (!customerUser) throw new Error(`no customer user for quote ${quote.id}`)
    if (!supplier) throw new Error(`no supplier for quote ${quote.id}`)

    const supplierCompanyName = CompanyUtils.getCompanyName(supplier)
    const authorizationDeadline = moment(
      quote.quoteDetails.authorization_deadline,
    )

    if (actionPerformer === ActionPerformer.Admin) {
      await emailService.sendToCompanyUsers(
        quote.company_id,
        EmailNotifications.quoteAuthorizationExtendedToSupplier({
          customerName: CompanyUtils.getCompanyName(customer),
          quoteNumber: quote.invoice_number,
          authorizationDeadline,
        }),
      )
    }

    if (quote.paymentDetails?.paymentType === InvoicePaymentType.FACTORING) {
      // no customer notifications for IHC quotes
      return
    }

    const smsMessage = SmsNotifications.quoteAuthorizationExtended({
      supplierName: supplierCompanyName,
      quoteNumber: quote.invoice_number,
      authorizationDeadline,
    })

    await Promise.allSettled([
      smsService.send(customerUser.phone, smsMessage),

      emailService.sendToCompanyUsersPersonalized(customer.id, (firstName) =>
        EmailNotifications.quoteAuthorizationExtended({
          customerName: firstName,
          supplierName: supplierCompanyName,
          quoteNumber: quote.invoice_number,
          authorizationDeadline,
        }),
      ),
    ])
  }

  public static async authorizationNearingExpiration(
    quote: IInvoice,
  ): Promise<void> {
    const [customer, loginUrl] = await Promise.all([
      findBuilderByAccountId(quote.customer_account_id),
      getLoginUrl(quote.company_id),
    ])

    if (!customer) throw new Error(`no customer for quote ${quote.id}`)

    const customerName = CompanyUtils.getCompanyName(customer)

    // if some day customer notifications are needed, don't send ARA notification to customer

    await emailService.sendToCompanyUsersPersonalized(
      quote.company_id,
      (firstName) =>
        EmailNotifications.quoteAuthorizationNearingExpirationToSupplier(
          quote.paymentDetails?.paymentType ?? null,
          {
            supplierName: firstName,
            customerName,
            quoteNumber: quote.invoice_number,
            loginUrl,
          },
        ),
    )
  }

  public static async authorizationExpired(
    quote: IInvoice,
    customerCompanyId: string,
    session: ClientSession | null,
  ) {
    const [supplier, customer, customerUser, loginUrl] = await Promise.all([
      Company.findById(quote.company_id),
      Company.findById(customerCompanyId),
      getOwningUser(customerCompanyId),
      getLoginUrl(quote.company_id),
    ])

    // prettier-ignore
    {
      if (!customer) throw new Error(`no customer found for quote ${quote.id}`)
      if (!supplier) throw new Error(`no supplier found for quote ${quote.id}`)
      if (!customerUser) throw new Error(`no customer user found for quote ${quote.id}`)
    }

    this.logger.info('sending authorization expired notifications')

    const customerCompanyName = CompanyUtils.getCompanyName(customer)
    const supplierCompanyName = CompanyUtils.getCompanyName(supplier)

    if (quote.paymentDetails?.paymentType === InvoicePaymentType.FACTORING) {
      // no customer notifications for IHC quotes
      return emailService.sendToCompanyUsersPersonalized(
        quote.company_id,
        (firstName) =>
          EmailNotifications.quoteAuthorizationExpiredToSupplier(
            quote.paymentDetails?.paymentType ?? null,
            {
              supplierName: firstName,
              customerName: customerCompanyName,
              quoteNumber: quote.invoice_number,
              loginUrl,
            },
          ),
      )
    }

    const smsMessage = SmsNotifications.quoteAuthorizationExpired({
      supplierName: supplier.name,
      quoteNumber: quote.invoice_number,
    })

    const webNotification = this.createWebNotification(
      quote,
      supplier.id,
      customer.id,
      notificationTypes.quoteExpired,
      `${quote.invoice_number} is expired`,
    )

    await Promise.allSettled([
      smsService.send(customerUser.phone, smsMessage),

      emailService.sendToCompanyUsersPersonalized(
        customerCompanyId,
        (firstName) =>
          EmailNotifications.quoteAuthorizationExpired({
            customerName: firstName,
            supplierName: supplierCompanyName,
            quoteNumber: quote.invoice_number,
          }),
      ),

      emailService.sendToCompanyUsersPersonalized(
        quote.company_id,
        (firstName) =>
          EmailNotifications.quoteAuthorizationExpiredToSupplier(
            quote.paymentDetails?.paymentType ?? null,
            {
              supplierName: firstName,
              customerName: customerCompanyName,
              quoteNumber: quote.invoice_number,
              loginUrl,
            },
          ),
      ),

      WebNotificationsService.send(webNotification, session),
    ])
  }

  private static async quoteCreateToInvitedUser(
    quote: IInvoice,
    supplier: ICompany,
  ) {
    // prettier-ignore
    this.logger.info(`no customer company for quote ${quote.id}, using customer account as contact info source`)

    const loginUrl = getApiURL()
    const customerAccount = await CustomerAccount.findById(
      quote.customer_account_id,
    )

    if (!customerAccount) throw new Error(`no customer account for ${quote.id}`)

    const quoteLink = await getInvoiceLink(quote.id, null, loginUrl)
    const supplierCompanyName = CompanyUtils.getCompanyName(supplier)

    const smsNotification = SmsNotifications.quoteCreated({
      supplierName: supplierCompanyName,
      quoteAmount: quote.total_amount,
      quoteLink,
    })

    const emailNotification = EmailNotifications.quoteCreated({
      customerName: customerAccount.first_name ?? '',
      supplierName: supplierCompanyName,
      quoteAmount: quote.total_amount,
      quoteLink,
    })

    await Promise.all([
      smsService.send(customerAccount.phone, smsNotification),
      emailService.sendTo(customerAccount.email, emailNotification),
    ])
  }

  private static createWebNotification(
    receivable: IInvoice,
    senderId: string,
    receiverId: string,
    type: NotificationTypes,
    content: string,
  ) {
    const webNotification: Partial<INotificationModel> = {
      type: type,
      sender: { company_id: senderId },
      receiver: { company_id: receiverId },
      content: content,
      metadata: {
        // align naming with invoice-related notifications
        invoice_id: new mongoose.Types.ObjectId(receivable.id),
        // use here for compatibility with legacy notifications (auto mark as read on view invoice),
        // but avoid alert types, prefer using only notification.type to simplify logic
        alertType: alertTypes.sent,
      },
    }

    this.logger.info({ webNotification }, 'created web notification')

    return webNotification
  }
}
