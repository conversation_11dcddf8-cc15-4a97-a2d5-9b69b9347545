import React from 'react'
import { observer } from 'mobx-react-lite'
import ApiTable from '../../../../../ui/molecules/ApiTable'
import { Trans, useTranslation } from 'react-i18next'
import { InvoiceContextMenu } from './InvoiceContextMenu'
import { IReceivablesTableProps } from '../IReceivablesTableProps'
import { useTabInvoice } from '../../../../../store/ScreensStore/Supplier/supplierInvoiceTab'
import { toJS } from 'mobx'
import { EmptyTableWarning } from '../../Components/EmptyTableWarning'
import { useRefreshFocusedTab } from '../../../../../hooks/useRefreshFocusedTab'
import { EInvoiceType, InvoicePaymentType } from '@linqpal/models'
import { useReceivables } from '../../ReceivablesContext'
import { CellScrollContainer } from './InvoiceTableCellScrollContainer'
import { ReceivableStatusLabel } from '../../../../../ui/molecules/StatusLabel/ReceivableStatusLabel'
import { ArAdvanceStatusLabel } from '../../../../../ui/molecules/StatusLabel/ArAdvanceStatusLabel'
import { LoadingIndicator } from '../../../Settlements/LoadingIndicator'
import { TableTitle } from '../../Components/TableTitle'
import { TooltipIcon } from '../../../../../ui/atoms/TooltipIcon'
import { PopoverPlacements } from '@ui-kitten/components'
import {
  PaidInvoiceStatuses,
  PaymentProcessingInvoiceStatuses,
} from '../../../../Contractor/PayablesTab/enums'
import { currencyMask } from '../../../../../utils/helpers/masking'
import { InvoicePropertyGroup } from '../../../../../ui/molecules/InvoicePropertyGroup'

export const InvoicesTable = observer((props: IReceivablesTableProps) => {
  const { t } = useTranslation('global')

  const { refreshTable } = useTabInvoice()
  const { invoices: store } = useReceivables()

  useRefreshFocusedTab(store, refreshTable)

  const isPayNowPayment = (item: any) =>
    !!(
      props.paymentType &&
      item.invoice.paymentDetails?.paymentType === InvoicePaymentType.PAYNOW
    )

  const renderPayNowIcon = () => (
    <div style={styles.centeredCell}>
      <TooltipIcon
        tooltip={
          <Trans
            t={t}
            i18nKey={'Receivables.pay-now-tooltip'}
            components={{ bold: <strong />, br: <br /> }}
          />
        }
        tooltipPosition={PopoverPlacements.BOTTOM}
        size={18}
      />
    </div>
  )

  const renderInvoiceContextMenu = (item: any) => (
    <div style={styles.centeredCell}>
      <InvoiceContextMenu invoice={toJS(item.invoice)} />
    </div>
  )

  return (
    <>
      <TableTitle title={t('Receivables.invoice.list-title')} />
      {store.loading || !store.dataLoaded ? (
        <LoadingIndicator />
      ) : (
        <ApiTable
          data={store.tableData.items}
          count={store.tableData.totalCount}
          showFilters={false}
          searchable={false}
          columns={[
            {
              name: t('Receivables.invoice.number'),
              selector: 'invoice_number',
              sortable: true,
            },
            {
              name: t('Receivables.contact'),
              selector: 'contact',
              sortable: false,
            },
            {
              name: t('Receivables.payers-info-recipients'),
              selector: 'payersInfo',
              cell: (item: any) =>
                item.payersInfo?.length > 1 ? (
                  <CellScrollContainer values={item.payersInfo} />
                ) : (
                  item.payersInfo || 'N/A'
                ),
              sortable: false,
              omit: !!props.paymentType,
            },
            {
              name: t('Receivables.business'),
              selector: 'customer_name',
              sortable: true,
            },
            {
              name: props.paymentType
                ? t('Receivables.invoice-status')
                : t('Receivables.receivable-status'),
              selector: 'status',
              cell: (item: any) => (
                <ReceivableStatusLabel
                  receivable={item.invoice}
                  onPress={() => props.onView(item)}
                />
              ),
              sortable: true,
            },
            {
              name: t('Receivables.ar-advance.status-column'),
              selector: 'ar_advance_status',
              cell: (item: any) => (
                <ArAdvanceStatusLabel
                  status={item.ar_advance_status}
                  onPress={() => props.onView(item)}
                />
              ),
              sortable: true,
              omit: !props?.paymentType,
            },
            {
              name: 'Due Date',
              selector: 'invoice_due_date',
              sortable: true,
            },
            {
              name: t('Receivables.invoice-amount'),
              selector: 'total_amount',
              sortable: true,
            },
            {
              name: t('Receivables.late-fee'),
              selector: 'lateFee',
              cell: (item: any) =>
                item.lateFee ? currencyMask(item.lateFee) : '-',
              omit: !props?.paymentType,
            },
            {
              name: t('Receivables.amount-type'),
              width: '140px',
              cell: (item: any) => {
                const processingAmount = item.totalProcessingAmount
                  ? item.totalProcessingAmount
                  : PaymentProcessingInvoiceStatuses.includes(item.status)
                  ? item.totalAmount
                  : 0

                const remainingAmount =
                  PaymentProcessingInvoiceStatuses.includes(item.status) ||
                  PaidInvoiceStatuses.includes(item.status)
                    ? 0
                    : item.totalRemainingAmount

                return (
                  <div style={{ alignSelf: 'center', paddingTop: 24 }}>
                    <InvoicePropertyGroup
                      paidAmount={t('Receivables.paid-amount')}
                      processingAmount={t('Receivables.processing-amount')}
                      remainingAmount={t('Receivables.remaining-amount')}
                      hasProcessingAmount={!!processingAmount}
                      hasRemainingAmount={!!remainingAmount}
                    />
                  </div>
                )
              },
            },

            {
              name: t('Receivables.amount'),
              width: '137px',
              cell: (item: any) => {
                const paidAmount = item.totalPaidAmount
                  ? item.totalPaidAmount
                  : PaidInvoiceStatuses.includes(item.status)
                  ? item.totalAmount
                  : 0

                const processingAmount = item.totalProcessingAmount
                  ? item.totalProcessingAmount
                  : PaymentProcessingInvoiceStatuses.includes(item.status)
                  ? item.totalAmount
                  : 0

                const remainingAmount =
                  PaymentProcessingInvoiceStatuses.includes(item.status) ||
                  PaidInvoiceStatuses.includes(item.status)
                    ? 0
                    : item.totalRemainingAmount

                return (
                  <div style={{ alignSelf: 'center', paddingTop: 24 }}>
                    <InvoicePropertyGroup
                      paidAmount={currencyMask(paidAmount)}
                      processingAmount={currencyMask(processingAmount)}
                      remainingAmount={currencyMask(remainingAmount)}
                      hasProcessingAmount={!!processingAmount}
                      hasRemainingAmount={!!remainingAmount}
                      tintedTextStyle={{ fontWeight: '700' }}
                    />
                  </div>
                )
              },
            },
            {
              name: ' ',
              width: '60px',
              cell: (item) =>
                isPayNowPayment(item)
                  ? renderPayNowIcon()
                  : renderInvoiceContextMenu(item),
              compact: true,
              sortable: false,
            },
          ]}
          loading={store.loading}
          onItemPress={props.onView}
          onChangePage={(page: number) => store.setPage(page)}
          style={invoiceTableStyles}
          conditionalRowStyles={[
            {
              when: (row) => isPayNowPayment(row),
              style: styles.payNowRow,
            },
          ]}
          page={store.page}
          sortCol={store.sort.column}
          sortDir={store.sort.direction}
          setSortColDir={([column, direction]: string[]) =>
            store.setSort(column, direction)
          }
          emptyResult={<EmptyTableWarning type={EInvoiceType.INVOICE} />}
        />
      )}
    </>
  )
})

const styles = {
  centeredCell: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: '100%',
    marginLeft: 1.5,
  },
  payNowRow: {
    backgroundColor: '#F4F9FD',
    pointerEvents: 'none',
  },
}

const invoiceTableStyles = {
  headCells: {
    style: {
      paddingLeft: 10,
      paddingRight: 10,
      fontSize: 12,
      fontWeight: '700',
      backgroundColor: '#F8F9F9',
      alignItems: 'start',
      justifyContent: 'flex-start',
    },
    activeSortStyle: {
      fontSize: 12,
    },
    inactiveSortStyle: {
      fontSize: 12,
    },
  },
  cells: {
    style: {
      fontSize: 14,
      alignItems: 'center',
      justifyContent: 'flex-start',
      paddingLeft: 10,
      paddingRight: 10,
      minHeight: 70,
    },
  },
}
