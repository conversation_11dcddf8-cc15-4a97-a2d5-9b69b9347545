import {
  CButton,
  CCol,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CRow,
  CSpinner,
} from '@coreui/react'
import { INormalizedApplicationDraft, routes } from '@linqpal/models'
import React, { useEffect, useState } from 'react'
import printHtmlBlock from 'print-html-block'
import {
  DraftSections,
  OwnerTypes,
} from '@linqpal/models/src/dictionaries/UnifiedApplication'
import {
  IncompleteAppItem,
  SubmittedAppItem,
} from '../../user/SupplierApplication/DocumentViewer'

interface IResponse<R> extends Promise<R> {
  cancel(): void
}

interface Props {
  company: string
  show: boolean
  onClose(): void
  document?: INormalizedApplicationDraft
}

export default function ApplicationDetails({
  company,
  show = false,
  onClose,
  document,
}: Props) {
  const [app, setApp] = useState<any>({})

  const company_id = app.company_id || ''
  useEffect(() => {
    let active = true
    if (show && company_id !== company) {
      if (company_id) {
        setApp({})
      }
      const req = routes.admin.contractorApplications(company) as IResponse<any>
      req
        .then((data) => {
          if (active) {
            setApp(
              data.item
                ? data.item
                : { error: 'Application not found', company_id: company },
            )
          }
        })
        .catch(
          (e) =>
            active &&
            setApp({ error: e.message || e.toString(), company_id: company }),
        )
      return () => {
        active = false
        req.cancel()
      }
    }
    return () => {
      active = false
    }
  }, [show, company_id, company])

  return (
    <CModal show={show} onClose={onClose}>
      <CModalHeader>
        <CModalTitle>
          Contractor`s application
          <CButton
            onClick={() => {
              printHtmlBlock('#printable', {
                importStyle: true,
              })
            }}
          >
            Print
          </CButton>
        </CModalTitle>
      </CModalHeader>
      <CModalBody>
        {app.error ? (
          <div className="p-3 d-flex flex-column align-items-center">
            {app.error}
          </div>
        ) : !app._id ? (
          <div className="p-3 d-flex flex-column align-items-center">
            <CSpinner />
          </div>
        ) : (
          <div id="printable">
            {document?.businessInfo_businessName
              ? DraftSections.map((section, i) => {
                  const t = Object.keys(document).filter((y) =>
                    y.includes(section),
                  )
                  if (t.length) {
                    return (
                      <SubmittedAppItem
                        i={i}
                        section={section}
                        sectionItems={t}
                        document={document}
                      />
                    )
                  } else {
                    return null
                  }
                })
              : Object.keys(app.data).map((g, i) => {
                  return (
                    <IncompleteAppItem
                      i={i}
                      section={g}
                      items={app.data[g].items}
                    />
                  )
                })}
          </div>
        )}
      </CModalBody>
      <CModalFooter>
        <CButton onClick={onClose}>Close</CButton>
      </CModalFooter>
    </CModal>
  )
}

export const Bank = ({ item }) => {
  return (
    <div>
      <CRow>
        <CCol xs={6}>Name:</CCol>
        <CCol xs={6}>{item.name}</CCol>
      </CRow>
      <CRow>
        <CCol xs={6}>Account holder Name:</CCol>
        <CCol xs={6}>{item.accountholderName}</CCol>
      </CRow>
      <CRow>
        <CCol xs={6}>Third Party Id:</CCol>
        <CCol xs={6}>{item.finicity?.accountId}</CCol>
      </CRow>
      <CRow>
        <CCol xs={6}>Routing Number:</CCol>
        <CCol xs={6}>{item.routingNumber}</CCol>
      </CRow>
      <CRow>
        <CCol xs={6}>IsManual Entry:</CCol>
        <CCol xs={6}>{JSON.stringify(item.isManualEntry)}</CCol>
      </CRow>
      <CRow>
        <CCol xs={6}>Payment Method Type:</CCol>
        <CCol xs={6}>{item.paymentMethodType}</CCol>
      </CRow>
      <CRow>
        <CCol xs={6}>Is Primary:</CCol>
        <CCol xs={6}>{JSON.stringify(item.isPrimary)}</CCol>
      </CRow>
      <CRow>
        <CCol xs={6}>Status:</CCol>
        <CCol xs={6}>{item.status}</CCol>
      </CRow>
      <CRow>
        <CCol xs={6}>Account Type:</CCol>
        <CCol xs={6}>{item.accountType}</CCol>
      </CRow>
      <CRow>
        <CCol xs={6}>Account Number:</CCol>
        <CCol xs={6}>{item.accountNumber}</CCol>
      </CRow>
    </div>
  )
}

export const CoOwner = ({ item }) => {
  const isLegacyApp = !item.type
  const isIndividual = isLegacyApp || item.type === OwnerTypes.INDIVIDUAL
  const isEntity = item.type === OwnerTypes.ENTITY

  return (
    <div>
      {isLegacyApp && (
        <CRow>
          <CCol>Title of owner:</CCol>
          <CCol>{item.titleOfOwner}</CCol>
        </CRow>
      )}
      <CRow>
        <CCol>Percent owned:</CCol>
        <CCol>{item.percentOwned}</CCol>
      </CRow>
      {isIndividual && (
        <>
          {isLegacyApp && (
            <CRow>
              <CCol>Other title of owner:</CCol>
              <CCol>{item.otherTitleOfOwner}</CCol>
            </CRow>
          )}
          <CRow>
            <CCol>First name:</CCol>
            <CCol>{item.firstName}</CCol>
          </CRow>
          <CRow>
            <CCol>Last Name:</CCol>
            <CCol>{item.lastName}</CCol>
          </CRow>
        </>
      )}
      {isEntity && (
        <>
          <CRow>
            <CCol>Entity Name:</CCol>
            <CCol>{item.entityName}</CCol>
          </CRow>
          <CRow>
            <CCol>Authorized Representative:</CCol>
            <CCol>{`${item.firstName} ${item.lastName}`}</CCol>
          </CRow>
        </>
      )}
      <CRow>
        <CCol>City:</CCol>
        <CCol>{item.city}</CCol>
      </CRow>
      <CRow>
        <CCol>State:</CCol>
        <CCol>{item.state}</CCol>
      </CRow>
      <CRow>
        <CCol>Address:</CCol>
        <CCol>{item.address}</CCol>
      </CRow>
      {isLegacyApp && (
        <CRow>
          <CCol>Unit number:</CCol>
          <CCol>{item.unitNumber}</CCol>
        </CRow>
      )}
      <CRow>
        <CCol>Zip:</CCol>
        <CCol>{item.zip}</CCol>
      </CRow>
      {isIndividual && (
        <>
          <CRow>
            <CCol>Birthday:</CCol>
            <CCol>{item.birthday}</CCol>
          </CRow>
          <CRow>
            <CCol>SSN:</CCol>
            <CCol>{item.ssn?.display}</CCol>
          </CRow>
        </>
      )}
      {isEntity && (
        <CRow>
          <CCol>EIN:</CCol>
          <CCol>{item.ein?.display}</CCol>
        </CRow>
      )}
      <CRow>
        <CCol>Email:</CCol>
        <CCol>{item.email}</CCol>
      </CRow>
      <CRow>
        <CCol>Phone:</CCol>
        <CCol>{item.phone}</CCol>
      </CRow>
    </div>
  )
}
