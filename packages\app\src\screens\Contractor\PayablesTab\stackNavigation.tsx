import React from 'react'
import { createStackNavigator } from '@react-navigation/stack'
import { paths } from '../../links'
import { InvoiceListing } from '../InvoicesTab/InvoiceListing/InvoiceListing'
import { InvoiceReceipt } from '../InvoicesTab/Receipt/InvoiceReceipt'
import ScreenSelector from '../InvoicesTab/Invoice/ScreenSelector'
import LinkBankAccount from '../MoreTab/Wallet/LinkPaymentScreens/LinkBankAccount'
import { SelectProject } from '../InvoicesTab/SelectProject'
import CreditDebitCard from '../AddCreditDebitCard'
import CompanyInvoicesPay from '../InvoicesTab/Invoice/CompanyInvoicesPay'
import { InvoicePreview } from '../InvoicesTab/InvoicePreview'
import { PaySupplierForm } from '../InvoicesTab/PaySupplierForm'
import { Payables } from './Payables'
import { VendorDetails } from './DetailsPages/VendorDetails'
import { InvoiceDetails } from '../InvoicesTab/Invoice/InvoiceDetails'
import { Source } from '../../../store/AddPaymentMethodStore'
import { MobileProjectForm } from '../../ProjectForm/MobileProjectForm'
import { QuestionnaireLayout } from '../../ProjectForm/QuestionnaireLayout'
import { PaymentHistoryInvoiceReceipt } from '../InvoicesTab/Receipt/PaymentHistoryInvoiceReceipt'

const Stack = createStackNavigator()

type SourceType = typeof Source[keyof typeof Source]

const screenConfig = (isMobile: boolean, source: SourceType, label = '') => [
  {
    name: paths.Console.Payables.home,
    component: isMobile ? InvoiceListing : Payables,
    options: isMobile ? undefined : { title: label },
  },
  {
    name: paths.Console.Payables.Vendor,
    component: isMobile ? ScreenSelector : VendorDetails,
    options: isMobile ? undefined : { title: label },
  },
  {
    name: paths.Console.Payables.Invoice,
    component: InvoiceDetails,
    options: isMobile ? undefined : { title: label },
  },
  ...(isMobile
    ? []
    : [
        {
          name: paths.Console.Payables.Quote,
          component: InvoiceDetails,
          options: { title: label },
        },
      ]),
  { name: paths.Console.Payables.Receipt, component: InvoiceReceipt },
  {
    name: paths.Console.Payables.PaymentHistory,
    component: PaymentHistoryInvoiceReceipt,
  },
  { name: paths.Console.Payables.Pay, component: CompanyInvoicesPay },
  { name: paths.Console.Payables.AttachmentPreview, component: InvoicePreview },
  { name: paths.Console.Payables.InvoiceForm, component: PaySupplierForm },
  {
    name: paths.LinkBank,
    component: (props) => <LinkBankAccount {...props} source={source} />,
  },
  {
    name: paths.LinkCard,
    component: (props) => <CreditDebitCard {...props} source={source} />,
  },
  { name: paths.Console.Payables.SelectProject, component: SelectProject },
  {
    name: paths.Console.Payables.NewProject,
    component: isMobile ? MobileProjectForm : QuestionnaireLayout,
  },
]

const renderScreens = (isMobile: boolean, source: SourceType, label?: string) =>
  screenConfig(isMobile, source, label).map(({ name, component, options }) => (
    <Stack.Screen
      key={name}
      name={name}
      component={component}
      options={options}
    />
  ))
export { renderScreens }
