import React, { FC } from 'react'
import { BtInput } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'

const Email: FC = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const handleChange = (email: string) => {
    store.document.data.businessInfo.email = email
  }

  return (
    <BtInput
      value={store.document.data.businessInfo.email || ''}
      onChangeText={handleChange}
      label={t('Business.EmailLabel')}
      testID="UnifiedApplication.BusinessInfo.Email"
    />
  )
})

export default {
  component: Email,
  title: {
    text: 'Business.Email',
  },
  showFooterMessage: true, // TODO: VK: Unified: used to display consent message on mobile, review
}
