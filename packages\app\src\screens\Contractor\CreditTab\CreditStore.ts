import { fileDownloader } from '@linqpal/common-frontend/src/helpers'
import { dictionaries, editableModel, routes, routes2 } from '@linqpal/models'
import {
  describeYourselfList as categoryList,
  LOAN_APPLICATION_STATUS,
  REPAYMENT_BY,
  VirtualCardStatus,
} from '@linqpal/models/src/dictionaries'
import {
  LOAN_PARAMETERS_CHANGE_TYPE,
  LOAN_REPAYMENT_SCHEDULE_STATUS,
  LOAN_REPAYMENT_STATUS,
  LOAN_REPAYMENT_TYPE,
  LOAN_STATUS,
  LOAN_TIMELINE_ITEM_TYPE,
  UI_LOAN_STATUS,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import { MAIN_ID } from '@linqpal/models/src/dictionaries/onboarding'
import { CardHolder, VirtualCard } from '@linqpal/models/src/mst/VirtualCard'
import isArray from 'lodash/isArray'
import makeInspectable from 'mobx-devtools-mst'
import { types } from 'mobx-state-tree'
import moment from 'moment'
import 'moment-timezone'
import 'moment-business-days'
import { Linking } from 'react-native'
import RootStore from '../../../store'
import { isWeb } from '../../../utils/helpers/commonUtils'
import numbro from 'numbro'
import { alertDisplayData } from './modals/LoanPastDueModal/alertDisplayData'
import { toJS } from 'mobx'
import { round } from '../../../utils/helpers/round'
import { roundVal } from './LoanSchedule/roundVal'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'

const {
  PAST_DUE_LATE,
  PAST_DUE,
  DUE_NEXT,
  PAST_DUE_LATE_AND_INTEREST,
  PAST_DUE_PENALTY,
} = UI_LOAN_STATUS

const { LATE_FEE, EXTENSION_FEE } = LOAN_TIMELINE_ITEM_TYPE

const { POSTPONED, RESCHEDULED } = LOAN_REPAYMENT_SCHEDULE_STATUS

const props = {
  creditAppliedInvoices: types.maybeNull(types.array(types.frozen())),
  virtualCard: types.maybeNull(VirtualCard),
  cardHolder: types.maybeNull(CardHolder),
  loansWithUnusedCards: types.optional(types.array(types.frozen()), []),
  approvedLoans: types.optional(types.array(types.frozen()), []),
  authorizedQuotes: types.optional(types.array(types.frozen()), []),
  loading: types.optional(types.boolean, false),
  loan: types.maybeNull(types.frozen()),
  company: types.maybeNull(types.frozen()),
  supplier: types.maybeNull(types.frozen()),
  chosenLoan: types.maybeNull(types.frozen()),
  bottomSheetVisible: types.optional(types.boolean, false),
  overdueLoans: types.optional(types.array(types.frozen()), []),
  chosenSupplierName: types.optional(types.string, ''),
  supplierRepays: types.optional(types.boolean, false),
  loanApplication: types.maybeNull(types.frozen()),
  alertData: types.maybeNull(types.frozen()),
}

function views(self) {
  return {
    get categoryToPrefill() {
      // Mapping added until unified application is released because onboarding categories and categories in credit app are different
      const onboardingType = RootStore.userStore.settings?.get(
        'onboardingType',
      ) as string[]
      const type = onboardingType ? onboardingType[0] : ''
      switch (type) {
        case MAIN_ID.SUB_CONTRACTOR:
          return categoryList[1].value
        case MAIN_ID.GENERAL_CONTRACTOR:
          return categoryList[0].value
        case MAIN_ID.DEALER_RETAILER_SUPPLIER:
          return categoryList[6].value
        case MAIN_ID.MANUFACTURER_DISTRIBUTOR:
          return categoryList[4].value
        case MAIN_ID.DEVELOPER_PROPERTY_OWNER:
          return categoryList[2].value
        default:
          return categoryList[7].value
      }
    },
    get hasUnusedVirtualCard() {
      return !!self.loansWithUnusedCards.length
    },
    get unusedActiveCards() {
      const active = self.loansWithUnusedCards.filter(
        (c) => !self.isExpired(c.card?.status),
      )
      return active
    },
    get hasUnsedActiveCards() {
      const active = self.unusedActiveCards
      return !!active.length
    },
    get isPrequalifiedApplication() {
      const loanApplication = RootStore.userStore.document
      return !loanApplication?.invoice_id
    },
    get shouldGoToCreditHome() {
      // when all existing cards are expired and there are no active loans and user has submitted another application, show credit home with processing info
      return (
        self.hasUnusedVirtualCard &&
        !self.hasUnsedActiveCards &&
        !self.approvedLoans.length &&
        ![
          LOAN_APPLICATION_STATUS.APPROVED,
          LOAN_APPLICATION_STATUS.EXPIRED,
        ].find((stat) => RootStore.userStore.document?.loanStatus === stat)
      )
    },
    get loanAmountDue() {
      return (
        self?.loan?.amount +
        self?.loan?.fee -
        self?.totalLoanAmountPaid(self?.loan)
      )
    },
    get isPaidOff() {
      return self.loan?.status === LOAN_STATUS.CLOSED
    },
    get creditDecisionMade() {
      const { settings, document } = RootStore.userStore
      return (
        settings.get('previouslyApproved') ||
        document?.loanStatus === LOAN_APPLICATION_STATUS.REJECTED
      )
    },
    get previousLoans(): any[] {
      return self.approvedLoans.filter(
        (loan) => loan.lms?.status === LOAN_STATUS.CLOSED,
      )
    },
    get activeLoans(): any[] {
      return self.approvedLoans.filter(
        (loan) =>
          ![LOAN_STATUS.CLOSED, LOAN_STATUS.CANCELED].includes(
            loan.lms?.status,
          ),
      )
    },
    get totalExtensionFee(): number {
      const total = self.loan.activeReceivables?.reduce((sum, l) => {
        if (l.type === LOAN_REPAYMENT_TYPE.EXTENSION_FEE)
          return sum + l.expectedAmount
        else return sum
      }, 0)
      return total
    },
    get numberOfInstallments(): string {
      const active = self.loan?.loanTemplate
      const label = active.installmentsNumber > 1 ? 'Payments' : 'Payment'
      if (
        active.changeType === LOAN_PARAMETERS_CHANGE_TYPE.RESCHEDULE ||
        active.installmentsNumber === 1
      ) {
        return `${active.installmentsNumber} ${label}`
      } else {
        return `${active.installmentsNumber} weekly payments`
      }
    },
    get penaltySum(): number {
      const sum = this.activeLoans.reduce((s, item) => {
        return s + item.lms?.loanDetails?.totalDailyPenaltyInterest || 0
      }, 0)
      return sum
    },
    get processingSum(): number {
      const sum = this.activeLoans.reduce((s, item) => {
        return s + item.lms?.loanDetails?.totalProcessingPaymentsAmount || 0
      }, 0)
      return sum
    },
    get totalPastDueAmount(): number {
      const sum = this.activeLoans.reduce((s, item) => {
        return s + item.lms?.loanDetails?.lateAmount || 0
      }, 0)
      return sum
    },
  }
}

function actions(self) {
  return {
    setLoan: (l) => {
      self.loan = l
    },
    setCompany: (company) => {
      self.company = company
    },
    setSupplier: (supplier) => {
      self.supplier = supplier
    },
    setLoading: (value) => {
      self.loading = value
    },
    setLoanApplication: (app) => {
      self.loanApplication = app
    },
    isExpired(status) {
      return status === dictionaries.VirtualCardStatus.EXPIRED
    },
    isPrequalExpired(expiry) {
      return moment().toDate() > new Date(expiry || '')
    },
    createLoanApp(onSuccess) {
      routes2.user.loanApplication().then(() => {
        RootStore.userStore.fetchDocument(ApplicationType.Credit)
        onSuccess && onSuccess()
      })
    },
    setSupplierPays(val) {
      self.supplierRepays = val
    },
    setCreditAppliedInvoices: (inv) => {
      const invs = Array.isArray(inv) ? inv : [inv]
      const invoices: any[] = [...invs]
      self.creditAppliedInvoices = invoices
      self.setLoading(false)
    },
    setVirtualCard: (v) => {
      self.virtualCard = VirtualCard.create(v)
    },
    setCardHolder: (c) => {
      self.cardHolder = CardHolder.create(c)
    },
    setLoansWithUnusedCards: (list) => {
      self.loansWithUnusedCards = list
    },
    setApprovedLoans: (list) => {
      self.approvedLoans = list
    },
    setAuthorizedQuotes: (list) => {
      self.authorizedQuotes = list
    },
    setAlertData: (val) => {
      self.alertData = val
    },
    getPastdueAlertType: async () => {
      const pastdueLatefee = self.overdueLoans.filter(
        (l) => l.lms?.currentStatus === PAST_DUE_LATE,
      )
      if (pastdueLatefee.length > 1) {
        let lateSum = 0
        pastdueLatefee.forEach(
          (p) => (lateSum += self.totalUnpaidLatefee(p.lms)),
        )
        self.setAlertData({
          ...alertDisplayData.manyPastdueWithLatefee,
          lateFee: lateSum,
        })
      } else if (self.overdueLoans.length > 1)
        self.setAlertData(alertDisplayData.manyPastdueLoans)
      else {
        const pendingLateFee = self.totalUnpaidLatefee(
          self.overdueLoans[0]?.lms,
        )
        if (pendingLateFee) {
          self.setAlertData({
            ...alertDisplayData.singlePastDueLoanWithLateFee,
            lateFee: pendingLateFee,
          })
        } else {
          self.setAlertData(alertDisplayData.singlePastDueLoan)
        }
      }
    },
    fetchCreditAppliedInvoices: async (invId) => {
      const id = invId || RootStore.userStore.document?.invoice_id
      if (id) {
        const resp = await routes.invoices.show({ id })
        self.setCreditAppliedInvoices(resp.invoices)
      }
    },
    fetchVirtualCard: async (cardId) => {
      self.setLoading(true)
      try {
        const resp = await routes2.card.getVirtualCard({ cardId })
        self.setVirtualCard(resp.card)
        return resp
      } finally {
        self.setLoading(false)
      }
    },
    fetchLoans: async () => {
      self.setLoading(true)
      const resp = await routes.contractor.loans()
      const cards = resp.loans.filter((l) => {
        return l.invoiceDetails?.cardId && l.card && !l.card.useDate
      })

      self.setLoansWithUnusedCards(cards)

      const updated = await Promise.all(
        cards.map(async (l) => {
          if (l.card?.status === VirtualCardStatus.EXPIRED) {
            return l
          } else {
            try {
              const res = await routes2.card.getVirtualCard({
                cardId: l.invoiceDetails?.cardId,
              })
              return {
                ...l,
                cbwCardInfo: res.card,
                holder: res.holder,
                invoice: l.invoices[0],
              }
            } catch (e) {
              return l
            }
          }
        }),
      )

      self.setLoansWithUnusedCards(updated)

      const loans = resp.loans
        .filter((loan) => self.isLoanIdExists(loan))
        .sort((loan1, loan2) => {
          // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
          // move auto trade credit draws with pending disbursement to the top
          const firstIsAutoTradeCredit =
            loan1.metadata?.repayment?.autoTradeCreditEnabled &&
            loan1.status === LOAN_APPLICATION_STATUS.PROCESSING
          const secondIsAutoTradeCredit =
            loan2.metadata?.repayment?.autoTradeCreditEnabled &&
            loan2.status === LOAN_APPLICATION_STATUS.PROCESSING

          if (firstIsAutoTradeCredit && !secondIsAutoTradeCredit) {
            return -1
          } else if (!firstIsAutoTradeCredit && secondIsAutoTradeCredit) {
            return 1
          } else {
            return moment(loan2.createdAt).diff(moment(loan1.createdAt))
          }
        })

      const approvedLoans = loans

      const authorizedQuotes = resp.loans.filter(
        (loan) => loan.status === LOAN_APPLICATION_STATUS.AUTHORIZED,
      )

      const pastDueLoans = resp.loans.filter((loan) => {
        return (
          [
            PAST_DUE,
            PAST_DUE_LATE,
            PAST_DUE_LATE_AND_INTEREST,
            PAST_DUE_PENALTY,
          ].includes(loan.lms?.currentStatus) &&
          ![LOAN_STATUS.CLOSED, LOAN_STATUS.CANCELED].includes(loan.lms?.status)
        )
      })
      self.setOverDueLoans(pastDueLoans)
      pastDueLoans.length && (await self.getPastdueAlertType())
      self.setApprovedLoans(approvedLoans)
      self.setAuthorizedQuotes(authorizedQuotes)
      self.setLoading(false)
    },
    getLoan: (invoiceId) => {
      const res = self.loansWithUnusedCards.find((l) => {
        const inv = l.invoiceDetails.invoiceId
        return isArray(inv)
          ? inv.includes(invoiceId)
          : l.invoiceDetails.invoiceId === invoiceId
      })
      return res
    },
    findLoanSetVirtualCard: (invoiceId) => {
      const res = self.getLoan(invoiceId)
      res && self.setVirtualCard(res.cbwCardInfo)
      if (res) {
        self.setVirtualCard(res.cbwCardInfo)
        self.setCardHolder(res.holder)
      }
    },
    isLoanIdExists: (loan) => {
      return loan?.lms_id || loan?.loanpro_id
    },
    fetchLoan: (l) => {
      self.setLoading(true)
      routes.contractor
        .loan(l.lms_id || l.loanpro_id)
        .then((r) => {
          if (r.loan) {
            self.setLoan({ ...r.loan, cardId: l.card?.cardId })
          }
          if (r.company) {
            self.setCompany({ ...r.company })
          }
          self.setSupplierPays(
            r.loanApplication?.metadata?.repayment?.loanPaymentCollection ===
              REPAYMENT_BY.SUPPLIER,
          )
          if (r.loanApplication && r.loanApplication.invoiceDetails) {
            self.setLoanApplication(r.loanApplication)
            self.fetchCreditAppliedInvoices(
              r.loanApplication.invoiceDetails.invoiceId,
            )
          }
        })
        .finally(() => self.setLoading(false))
    },
    totalLoanAmountPaid: (loan) => {
      return loan?.loanDetails.totalPaid || 0
    },
    canPay: (loan) => {
      const { totalProcessingPaymentsAmount, totalLoanAmount, totalPaid } =
        loan?.loanDetails || {}
      return (
        roundVal(totalProcessingPaymentsAmount + totalPaid) < totalLoanAmount
      )
    },
    totalLoanPastDueScheduledPayment: (loan) => {
      const total = loan?.activeReceivables?.reduce((amt, l) => {
        if (
          l.type === LOAN_REPAYMENT_TYPE.INSTALLMENT &&
          l.status === LOAN_REPAYMENT_STATUS.LATE
        ) {
          return amt + (l.expectedAmount - l.processingAmount - l.paidAmount)
        } else return amt
      }, 0)
      return total
    },
    totalLoanPastDueExtensionFee: (loan) => {
      const total = loan?.activeReceivables?.reduce((amt, l) => {
        if (
          l.type === LOAN_REPAYMENT_TYPE.EXTENSION_FEE &&
          l.status === LOAN_REPAYMENT_STATUS.LATE
        ) {
          return amt + (l.expectedAmount - l.processingAmount - l.paidAmount)
        } else return amt
      }, 0)
      return total
    },
    totalLoanExtensionFee: (loan) => {
      const total = loan?.activeReceivables?.reduce((amt, l) => {
        if (
          l.type === LOAN_REPAYMENT_TYPE.EXTENSION_FEE &&
          l.status === LOAN_REPAYMENT_STATUS.DUENEXT
        ) {
          return amt + (l.expectedAmount - l.processingAmount - l.paidAmount)
        } else return amt
      }, 0)
      return total
    },
    totalLoanPastDueFee: (loan) => {
      const lateFees = loan?.activeReceivables?.filter((l) =>
        [
          LOAN_REPAYMENT_TYPE.LATE_FEE,
          LOAN_REPAYMENT_TYPE.MANUAL_LATE_FEE,
        ].includes(l.type),
      )

      const lateFeesTotal = lateFees.reduce((total: number, fee: any) => {
        return (
          total + (fee.expectedAmount - fee.processingAmount - fee.paidAmount)
        )
      }, 0)

      return lateFeesTotal
    },
    totalUnpaidLatefee: (loan) => {
      const total = loan?.activeReceivables?.reduce((amt, l) => {
        if (
          [
            LOAN_REPAYMENT_TYPE.LATE_FEE,
            LOAN_REPAYMENT_TYPE.MANUAL_LATE_FEE,
          ].includes(l.type) &&
          [LOAN_REPAYMENT_STATUS.PENDING, LOAN_REPAYMENT_STATUS.LATE].includes(
            l.status,
          )
        ) {
          return amt + (l.expectedAmount - l.paidAmount)
        } else return amt
      }, 0)
      return total
    },
    setChosenLoan: (loan) => {
      self.chosenLoan = loan
    },
    setBottomSheetVisible: (flag: boolean) => {
      self.bottomSheetVisible = flag
    },
    setOverDueLoans(loans) {
      self.overdueLoans = loans
    },
    findMissedInstallments(lms) {
      return lms?.installments?.filter(
        (i) =>
          i.status === LOAN_REPAYMENT_STATUS.LATE &&
          i.expectedAmount !== i.paidAmount,
      )
    },
    totalMissedAmount(missedList) {
      return missedList
        ?.reduce((sum, mi) => {
          return Number(
            numbro(sum + (mi.expectedAmount - mi.paidAmount)).format({
              mantissa: 2,
            }),
          )
        }, 0)
        .toString()
    },
    onClickLink(url) {
      if (isWeb) {
        window.open(url, '_blank')
      } else {
        Linking.openURL(url)
      }
    },
    onClickMasterLink() {
      routes.contractor
        .makeAgreement({ agreement_type: 'master' })
        .then((res) => {
          if (res.url) {
            if (isWeb) {
              fileDownloader(res.url, {}, res.fileName)
            } else {
              Linking.openURL(res.url)
            }
          }
        })
    },
    bankAccountLabel(application: any) {
      const appBank = toJS(application)
        ?.data?.get('bank')
        ?.items.find((item) => item.identifier === 'details')?.content
      return `Bank account ${appBank?.accountNumber}`
    },
    bankAccountValue(application: any) {
      const { name, accountNumber } = application.draft?.bank_details || {}

      const fundingName = name?.length > 20 ? name.substr(0, 20) + '...' : name
      const fundingNumber = accountNumber ? accountNumber.substr(-8) : '-'
      return `${fundingName} ${fundingNumber}`
    },
    getLatestPastDueReceivable(loan) {
      const today = moment().utc().startOf('day')
      const active = loan?.lms?.activeReceivables
        ? [...loan?.lms?.activeReceivables]
        : []
      const pending = active.reverse().find((l) => {
        const expected = moment.tz(l.expectedDate, 'UTC')
        return (
          [LOAN_REPAYMENT_STATUS.PENDING, LOAN_REPAYMENT_STATUS.LATE].includes(
            l.status,
          ) && expected.isBefore(today, 'day')
        )
      })
      return pending
    },
    getDaysDifference(dateUTC) {
      const today = moment().utc().startOf('day')
      return today.businessDiff(dateUTC)
    },
    getPastDue(currentStatus: string, lateAmount: number) {
      return currentStatus === DUE_NEXT ? 0 : lateAmount
    },
    getPenalty(currentStatus: string, penaltyAmount: number) {
      return [PAST_DUE_LATE_AND_INTEREST, PAST_DUE_PENALTY].includes(
        currentStatus,
      )
        ? penaltyAmount
        : 0
    },
    lateFeePending(): boolean {
      const pending = self.loan?.activeReceivables.find(
        (lr) =>
          lr.type === LOAN_REPAYMENT_TYPE.LATE_FEE &&
          [LOAN_REPAYMENT_STATUS.PENDING, LOAN_REPAYMENT_STATUS.LATE].includes(
            lr.status,
          ),
      )
      return !!pending
    },
    penaltyInterestPending(): boolean {
      const pending = self.loan?.activeReceivables.find(
        (lr) =>
          lr.type === LOAN_REPAYMENT_TYPE.PENALTY_INTEREST_FEE &&
          [LOAN_REPAYMENT_STATUS.PENDING, LOAN_REPAYMENT_STATUS.LATE].includes(
            lr.status,
          ),
      )
      return !!pending
    },
    getPartialProcessingDetails(
      loanDetails: any,
      timelineItems: any[],
    ): { loanReceivableId: string; amount: number } {
      const {
        totalProcessingPaymentsAmount: processingSum,
        totalDailyPenaltyInterest,
      } = loanDetails
      const res = { loanReceivableId: '', amount: 0 }
      if (round(processingSum) > 0) {
        let sum = round(processingSum) - round(totalDailyPenaltyInterest)
        const pastDueFees = timelineItems.filter(
          (item) =>
            [LATE_FEE, EXTENSION_FEE].includes(item.itemType) &&
            ![POSTPONED, RESCHEDULED].includes(item.dueStatus),
        )
        const pastDueInstallments = timelineItems.filter(
          (item) =>
            item.itemType === LOAN_TIMELINE_ITEM_TYPE.INSTALLMENT &&
            ![POSTPONED, RESCHEDULED].includes(item.dueStatus),
        )
        const orderedList = [...pastDueFees, ...pastDueInstallments]
        orderedList.forEach((r) => {
          if (sum >= r.amount) {
            sum = round(sum - r.amount)
            timelineItems.map((item) => {
              if (item.loanReceivableId === r.loanReceivableId) {
                item.dueStatus = LOAN_REPAYMENT_STATUS.PROCESSING
              }
              return item
            })
          } else if (!res.loanReceivableId && sum) {
            res.loanReceivableId = r.loanReceivableId
            res.amount = sum
            timelineItems.map((item) => {
              if (item.loanReceivableId === r.loanReceivableId)
                item.amount = round(item.amount - sum)
              return item
            })
          }
        })
      }
      return res
    },
  }
}

export const CreditStoreModel = types
  .compose(types.model(props).views(views).actions(actions), editableModel())
  .named('CreditStore')
const store = CreditStoreModel.create({})
makeInspectable(store)
export default store
