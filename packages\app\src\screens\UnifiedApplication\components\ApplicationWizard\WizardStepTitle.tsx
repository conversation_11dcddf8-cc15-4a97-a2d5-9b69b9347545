import React, { FC } from 'react'
import { useResponsive } from '../../../../utils/hooks'
import { useTranslation } from 'react-i18next'
import { StyleSheet, View } from 'react-native'
import { BtPlainText } from '@linqpal/components/src/ui'
import { IApplicationStepEditorTitle } from '../../flow/ApplicationEditorSelector'

interface IProps {
  title?: IApplicationStepEditorTitle
}

export const WizardStepTitle: FC<IProps> = ({ title }) => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  console.log('=>(ApplicationStepTitle.tsx:33) title', title)

  const textSource = title?.text
  if (!textSource) return null

  const text = textSource instanceof Function ? textSource() : textSource

  return (
    <View style={title?.wrapperStyle}>
      <BtPlainText
        style={[styles.title, sm ? styles.titleDesktop : styles.titleMobile]}
      >
        {t(text as any)}
      </BtPlainText>
    </View>
  )
}

const styles = StyleSheet.create({
  title: {
    fontWeight: '600',
    color: '#003353',
    marginBottom: 10,
  },
  titleDesktop: {
    fontSize: 24,
    lineHeight: 36,
  },
  titleMobile: {
    fontSize: 18,
    lineHeight: 26,
  },
})
