import { makeAutoObservable } from 'mobx'
import {
  IUnifiedApplicationData,
  IUnifiedApplicationDraft,
} from '@linqpal/models/src/types/unifiedApplication/IUnifiedApplicationDraft'
import { UnifiedApplicationDraftValidator } from '../../../../../models/src/types/unifiedApplication/UnifiedApplicationDraftValidator'
import { IUnifiedApplicationOptions } from '../../../../../models/src/types/unifiedApplication/IUnifiedApplicationOptions'

export class UnifiedApplicationDraft implements IUnifiedApplicationDraft {
  data: IUnifiedApplicationData

  initialStep: string

  currentStep: string

  private _validator: UnifiedApplicationDraftValidator

  constructor(data?: IUnifiedApplicationDraft) {
    this.initialStep = data?.initialStep || ''
    this.currentStep = data?.currentStep || data?.initialStep || ''

    this.data = {
      businessInfo: data?.data?.businessInfo || {},
      finance: data?.data?.finance || {},
      businessOwner: data?.data?.businessOwner || {},
      coOwnerInfo: data?.data?.coOwnerInfo || {},
      bank: data?.data?.bank || {},
      review: data?.data?.review || {},
    }

    this._validator = new UnifiedApplicationDraftValidator(this)

    makeAutoObservable(this)
  }

  validate(path: string, options: IUnifiedApplicationOptions): boolean {
    return this._validator.validate(path, options)
  }
}
