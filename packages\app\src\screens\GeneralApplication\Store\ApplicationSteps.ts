type Step = {
  _: string
  path: string
}

const createSteps = <TGroups extends Record<string, readonly string[]>>(
  groups: TGroups,
) => {
  // enrich step constants with group name, key and full path, so we can use it as
  // underscore is a shorthand for self-name of a group or step
  //
  // Steps.businessInfo._ -> 'businessInfo'
  // Steps.businessInfo.email._ -> 'email
  // Steps.businessInfo.email.path -> 'businessInfo.email'

  const result: {
    [GroupName in keyof TGroups]: { _: GroupName } & {
      [StepName in TGroups[GroupName][number]]: Step
    }
  } = {} as any

  for (const groupName in groups) {
    const steps = groups[groupName]
    const group: any = {
      _: groupName,
    }

    for (const step of steps) {
      group[step] = {
        _: step,
        path: `${groupName}.${step}`,
      }
    }

    result[groupName] = group
  }

  return result
}

// prettier-ignore
export const Steps = createSteps({
  businessInfo: [
    'email',
    'category',
    'businessName',
    'trade',
    'businessPhone',
    'businessAddress',
    'startDate',
    'type',
    'ein',
  ],
  finance: [
    'revenue',
    'debt',
    'howMuchCredit',
    'arAdvanceRequestedLimit',
  ],
  businessOwner: [
    'isOwner',
    'ownershipPercentage',
    'isAuthorized',
    'authorizedDetails',
    'address',
    'birthdate',
    'ssn',
  ],
  coOwnerInfo: [
    'coOwners'
  ],
  bank: [
    'details'
  ],
  review: [
    'review',
    'preview',
    'agreement'
  ],
} as const)
