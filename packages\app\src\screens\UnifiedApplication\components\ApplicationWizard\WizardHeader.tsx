import { useResponsive } from '../../../../utils/hooks'
import { StyleSheet, TouchableOpacity, View } from 'react-native'
import { IconCloseSquare } from '../../../../assets/icons'
import { useTranslation } from 'react-i18next'
import ApplicationStore from '../../../GeneralApplication/Application/ApplicationStore'
import { BtPlainText } from '@linqpal/components/src/ui'
import { Spacer } from '../../../../ui/atoms'
import React from 'react'

interface IHeaderProps {
  title?: string
  canSkip?: boolean | string
  onSkip?: () => void
  onClose: () => void
}

interface ICloseButtonProps {
  disabled?: boolean
  onPress: () => void
}

export const WizardHeader = ({
  onClose,
  canSkip,
  onSkip,
  title,
}: IHeaderProps) => {
  const { sm } = useResponsive()

  return sm ? (
    <HeaderDesktop title={title} onClose={onClose} />
  ) : (
    <HeaderMobile
      title={title}
      canSkip={canSkip}
      onSkip={onSkip}
      onClose={onClose}
    />
  )
}

export const HeaderDesktop = ({ onClose, title }: IHeaderProps) => {
  const { t } = useTranslation('application')

  return (
    <View style={[styles.headerWrapper, styles.headerWrapperDesktop]}>
      <CloseButton
        onPress={onClose}
        disabled={ApplicationStore.buttonLoading}
      />

      <BtPlainText style={[styles.title, styles.titleDesktop]}>
        {title || t('SetUpAccount')}
      </BtPlainText>
      <Spacer width={50} />
    </View>
  )
}

export const HeaderMobile = ({
  onClose,
  canSkip,
  onSkip,
  title,
}: IHeaderProps) => {
  const { t } = useTranslation('application')

  return (
    <View style={[styles.headerWrapper, styles.headerWrapperMobile]}>
      <CloseButton
        onPress={onClose}
        disabled={ApplicationStore.buttonLoading}
      />

      <BtPlainText style={[styles.title, styles.titleMobile]}>
        {title || t('SetUpAccount')}
      </BtPlainText>

      {canSkip ? (
        <BtPlainText style={styles.skipButton} onPress={onSkip}>
          {t('Skip')}
        </BtPlainText>
      ) : (
        <Spacer width={50} />
      )}
    </View>
  )
}

const CloseButton = ({ onPress, disabled }: ICloseButtonProps) => {
  const { sm } = useResponsive()

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      style={[
        styles.closeButton,
        sm ? styles.closeButtonDesktop : styles.closeButtonMobile,
      ]}
    >
      <IconCloseSquare width={12} height={12} />
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  closeButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
  },
  closeButtonDesktop: {
    borderColor: '#E6EBEE',
    borderRadius: 6,
    borderWidth: 1,
  },
  closeButtonMobile: {
    borderRadius: 8,
    backgroundColor: '#F5F7F8',
  },
  headerWrapper: {
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomColor: '#EBEEEF',
    borderBottomWidth: 1,
  },
  headerWrapperDesktop: {
    height: 72,
    paddingHorizontal: 20,
  },
  headerWrapperMobile: {
    height: 54,
    paddingHorizontal: 10,
  },
  title: {
    flex: 1,
    textAlign: 'center',
  },
  titleDesktop: {
    fontSize: 20,
    fontWeight: '600',
  },
  titleMobile: {
    fontSize: 16,
    fontWeight: '500',
  },
  skipButton: {
    color: '#335C75',
    fontWeight: '500',
    marginRight: 10,
  },
})
