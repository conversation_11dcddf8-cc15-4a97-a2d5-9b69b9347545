import {
  BankAccount,
  Company,
  CompanyService,
  createPagination,
  CustomerAccount,
  Invoice,
  invoicesService,
  LoanApplication,
  LoanPaymentPlan,
  Operation,
  User,
  UserRole,
} from '@linqpal/common-backend'
import { authRequired } from '../../../services/auth.service'

import moment from 'moment-timezone'
import { dictionaries, EInvoiceType, InvoicePaymentType } from '@linqpal/models'
import {
  getInvoice,
  projectInvoiceStatus,
} from '../../../controllers/invoice.controller'
import { ControllerItem } from 'src/routes/controllerItem'
import { PipelineStage } from 'mongoose'
import { SortingOrder } from '@linqpal/models/src/dictionaries/global'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'

const { OPERATION_STATUS, OPERATION_TYPES, invoiceStatus } = dictionaries

export default {
  middlewares: { pre: [authRequired()] },
  get: async (req, res) => {
    if (req.query.id) {
      const inv = await getInvoice({
        _id: req.query.id as string,
        companyId: req.company!._id.toString(),
      })
      if (inv) {
        const company = await Company.findById(inv.company_id).exec()
        const operation = await Operation.findOne({
          owner_id: inv._id,
          type: 'invoice',
        })
        res.send({
          invoice: { ...inv.toObject(), company, operationId: operation?._id },
        })
      } else {
        res.send({ id: req.query.id }).status(404)
      }
    } else {
      const format = '%m/%d/%Y'
      const timezone = 'America/Chicago'

      const {
        search = '',
        sortDirection = SortingOrder.DESC,
        customer,
        type,
        paymentType,
      } = req.query
      const duration = parseInt(req.query.duration as string) || 0
      const status = req.query.status as string | undefined
      const sortColumn = (req.query.sortColumn as string) || 'createdAt'

      const query = {}
      if (duration && duration > 0) {
        const d = moment().subtract(duration, 'days').startOf('day').toDate()
        Object.assign(query, { invoice_date: { $gte: d } })
      }

      if (Array.isArray(type)) {
        Object.assign(query, { type: { $in: type } })
      } else {
        Object.assign(query, { type: type ?? EInvoiceType.INVOICE })
      }

      if (!customer) {
        if (paymentType === InvoicePaymentType.FACTORING) {
          Object.assign(query, {
            $or: [
              {
                'paymentDetails.paymentType': InvoicePaymentType.FACTORING,
              },
              {
                $and: [
                  {
                    'paymentDetails.paymentType': InvoicePaymentType.PAYNOW,
                  },
                  {
                    'paymentDetails.arAdvanceStatus': {
                      $in: [
                        ArAdvanceStatus.NotApplied,
                        ArAdvanceStatus.Rejected,
                      ],
                    },
                  },
                ],
              },
            ],
          })
        } else {
          let arAdvanceIsEnabled
          if (req.company?.settings) {
            arAdvanceIsEnabled = req.company.settings?.arAdvance?.isEnabled
          } else {
            const company = await Company.findById(
              req.company!._id.toString(),
            ).exec()

            arAdvanceIsEnabled = company?.settings?.arAdvance?.isEnabled
          }
          if (arAdvanceIsEnabled) {
            Object.assign(query, {
              $or: [
                {
                  'paymentDetails.paymentType': {
                    $ne: InvoicePaymentType.FACTORING,
                  },
                },
                {
                  paymentDetails: null,
                },
              ],
            })
          }
        }
      } else {
        Object.assign(query, { customer_account_id: customer })
      }

      const pipeline: PipelineStage[] = [
        {
          $match: {
            $and: [
              { company_id: { $ne: '' } },
              { company_id: { $eq: req.company!._id.toJSON() } },
            ],
            ...query,
            isDeleted: { $ne: true },
          },
        },
        {
          $project: {
            _id: { $toString: '$_id' },
            company_id: 1,
            customer_account_id: {
              $convert: {
                input: '$customer_account_id',
                to: 'objectId',
                onError: null,
              },
            },
            invoice_document: 1,
            payersInfo: 1,
            document_name: 1,
            invoice_date: 1,
            invoice_due_date: 1,
            expiration_date: { $ifNull: ['$expiration_date', null] },
            address: 1,
            unitNumber: 1,
            city: 1,
            state: 1,
            seen: { $ifNull: ['$seen', false] },
            zip: 1,
            addressType: 1,
            customer_user_id: 1,
            invoice_number: 1,
            quote_number: 1,
            total_amount: 1,
            material_subtotal: 1,
            tax_amount: 1,
            material_description: 1,
            transaction_ids: 1,
            note: 1,
            createdAt: 1,
            payer_id: 1,
            status: 1,
            createdBy: 1,
            dismiss_reasons: 1,
            type: 1,
            connector: 1,
            quoteDetails: 1,
            invoice_id: { $toString: '$_id' },
            paymentDetails: 1,
            lateFee: { $ifNull: ['$paymentDetails.fees', 0] },
          },
        },
        {
          $lookup: {
            from: CustomerAccount.collection.name,
            as: 'customer',
            localField: 'customer_account_id',
            foreignField: '_id',
            pipeline: [
              {
                $lookup: {
                  from: User.collection.name,
                  as: 'user',
                  let: {
                    phone: { $ifNull: ['$phone', ''] },
                    email: { $ifNull: ['$email', ''] },
                  },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $cond: {
                            if: { $ne: ['$$email', ''] },
                            then: {
                              $eq: ['$email', '$$email'],
                            },
                            else: {
                              $or: [
                                { $eq: ['$login', '$$email'] },
                                { $eq: ['$login', '$$phone'] },
                              ],
                            },
                          },
                        },
                      },
                    },
                    {
                      $lookup: {
                        from: UserRole.collection.name,
                        as: 'role',
                        localField: 'sub',
                        foreignField: 'sub',
                        pipeline: [
                          {
                            $addFields: {
                              company_id: { $toObjectId: '$company_id' },
                            },
                          },
                          {
                            $lookup: {
                              from: Company.collection.name,
                              as: 'company',
                              localField: 'company_id',
                              foreignField: '_id',
                              pipeline: [{ $project: { name: 1 } }],
                            },
                          },
                          {
                            $unwind: {
                              path: '$company',
                              preserveNullAndEmptyArrays: true,
                            },
                          },
                          { $project: { company: 1 } },
                        ],
                      },
                    },
                    {
                      $unwind: {
                        path: '$role',
                        preserveNullAndEmptyArrays: true,
                      },
                    },
                    { $project: { role: 1 } },
                  ],
                },
              },
              {
                $unwind: {
                  path: '$user',
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $set: {
                  email: {
                    $ifNull: ['$guestInfo.email', { $ifNull: ['$email', ''] }],
                  },
                  phone: {
                    $ifNull: ['$guestInfo.phone', { $ifNull: ['$phone', ''] }],
                  },
                },
              },
            ],
          },
        },
        { $unwind: { path: '$customer', preserveNullAndEmptyArrays: true } },
        {
          $set: {
            'customer.name': {
              $cond: [
                {
                  $and: [
                    {
                      $ifNull: ['$customer.user.role.company.name', false],
                    },
                    {
                      $ne: ['$customer.user.role.company.name', ''],
                    },
                  ],
                },
                '$customer.user.role.company.name',
                '$customer.name',
              ],
            },
          },
        },
        {
          $lookup: {
            from: Operation.collection.name,
            as: 'operation',
            localField: 'invoice_id',
            foreignField: 'owner_id',
            pipeline: [
              {
                $match: {
                  $expr: {
                    $or: [
                      {
                        $and: [
                          {
                            $in: [
                              '$status',
                              [
                                OPERATION_STATUS.PLACED,
                                OPERATION_STATUS.PROCESSING,
                                OPERATION_STATUS.SUCCESS,
                                OPERATION_STATUS.FAIL,
                              ],
                            ],
                          },
                          { $eq: ['$type', OPERATION_TYPES.INVOICE.PAYMENT] },
                        ],
                      },
                      {
                        $and: [
                          { $eq: ['$status', OPERATION_STATUS.SUCCESS] },
                          { $eq: ['$type', OPERATION_TYPES.INVOICE.REFUND] },
                        ],
                      },
                    ],
                  },
                },
              },
              { $sort: { createdAt: -1 } as const },
              { $limit: 1 },
            ],
          },
        },
        { $unwind: { path: '$operation', preserveNullAndEmptyArrays: true } },
        {
          $addFields: {
            invoice_id: ['$invoice_id'],
            totalPaidAmount: {
              $cond: [
                { $ifNull: ['$operation.paidAmount', false] },
                '$operation.paidAmount',
                {
                  $cond: [
                    { $eq: ['$operation.status', OPERATION_STATUS.SUCCESS] },
                    '$operation.amount',
                    0,
                  ],
                },
              ],
            },
          },
        },
        {
          $addFields: {
            totalProcessingAmount: {
              $cond: [
                { $ifNull: ['$operation.processingAmount', false] },
                '$operation.processingAmount',
                {
                  $cond: [
                    {
                      $and: [
                        {
                          $eq: [
                            '$operation.status',
                            OPERATION_STATUS.PROCESSING,
                          ],
                        },
                        { $eq: ['$totalPaidAmount', 0] },
                      ],
                    },
                    '$operation.amount',
                    0,
                  ],
                },
              ],
            },
          },
        },
        {
          $addFields: {
            totalRemainingAmount: {
              $round: [
                {
                  $subtract: [
                    {
                      $round: [
                        {
                          $add: [
                            { $ifNull: ['$operation.amount', '$total_amount'] },
                            { $ifNull: ['$lateFee', 0] },
                          ],
                        },
                        2,
                      ],
                    },
                    {
                      $round: [
                        {
                          $add: [
                            { $ifNull: ['$totalPaidAmount', 0] },
                            { $ifNull: ['$totalProcessingAmount', 0] },
                          ],
                        },
                        2,
                      ],
                    },
                  ],
                },
                2,
              ],
            },
          },
        },
        {
          $lookup: {
            from: LoanApplication.collection.name,
            as: 'loanApp',
            localField: 'invoice_id',
            foreignField: 'invoiceDetails.invoiceId',
            pipeline: [
              { $sort: { createdAt: -1 } as const },
              { $limit: 1 },
              {
                $project: {
                  status: 1,
                  lms_id: 1,
                  'metadata.repayment.autoTradeCreditEnabled': 1,
                },
              },
            ],
          },
        },
        { $unwind: { path: '$loanApp', preserveNullAndEmptyArrays: true } },

        {
          $lookup: {
            from: LoanPaymentPlan.collection.name,
            as: 'inHouseCreditTerm',
            let: {
              planId: {
                $convert: {
                  input: '$paymentDetails.loanPlanId',
                  to: 'objectId',
                  onError: null,
                },
              },
            },
            pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$planId'] } } }],
          },
        },
        {
          $unwind: {
            path: '$inHouseCreditTerm',
            preserveNullAndEmptyArrays: true,
          },
        },

        {
          $addFields: {
            operationStatus: { $ifNull: ['$operation.status', ''] },
          },
        },
        {
          $project: {
            invoice_document: 1,
            payersInfo: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ['$payersInfo', []] } }, 0] },
                then: {
                  $map: {
                    input: { $ifNull: ['$payersInfo', []] },
                    as: 'payer',
                    in: '$$payer.emailAddress',
                  },
                },
                else: null,
              },
            },
            document_name: 1,
            invoice_date: {
              $dateToString: { date: '$invoice_date', format, timezone },
            },
            invoice_date_to_sort: '$invoice_date',
            invoice_due_date: {
              $dateToString: { date: '$invoice_due_date', format, timezone },
            },
            expiration_date: {
              $dateToString: { date: '$expiration_date', format, timezone },
            },
            quoteDetails: 1,
            address: {
              $ifNull: [
                {
                  $concat: [
                    '$address',
                    {
                      $cond: [
                        { $lte: ['$city', ''] }, // null or empty
                        '',
                        { $concat: [', ', '$city'] },
                      ],
                    },
                    {
                      $cond: [
                        { $lte: ['$state', ''] }, // null or empty
                        '',
                        { $concat: [', ', '$state'] },
                      ],
                    },
                    {
                      $cond: [
                        { $lte: ['$zip', ''] }, // null or empty
                        '',
                        { $concat: [', ', '$zip'] },
                      ],
                    },
                  ],
                },
                '',
              ],
            },
            addressType: 1,
            customer_account_id: 1,
            customer_user_id: 1,
            invoice_number: 1,
            quote_number: 1,
            total_amount: 1,
            material_subtotal: 1,
            tax_amount: 1,
            material_description: 1,
            transaction_ids: {
              $cond: {
                if: { $ne: [{ $type: '$transaction_ids' }, 'array'] },
                then: [],
                else: '$transaction_ids',
              },
            },
            note: 1,
            createdAt: 1,
            customer: 1,
            operation: 1,
            payer_id: 1,
            seen: 1,
            status: projectInvoiceStatus(req.company!._id.toString()),
            company_id: 1,
            createdBy: 1,
            dismiss_reasons: 1,
            type: 1,
            connector: 1,
            paymentDetails: 1,
            inHouseCreditTerm: 1,
            totalPaidAmount: 1,
            totalProcessingAmount: 1,
            totalRemainingAmount: 1,
            lateFee: 1,
          },
        },
      ]

      if (req.company?.settings?.supplierCanPay) {
        pipeline.push({
          $lookup: {
            from: BankAccount.collection.name,
            localField: 'customer.bankAccounts',
            foreignField: '_id',
            as: 'customer.bankAccounts',
            pipeline: [
              {
                $match: {
                  $or: [
                    { isDeactivated: { $exists: true, $eq: false } },
                    { isDeactivated: { $exists: false } },
                    { isDeactivated: { $exists: true, $eq: null } },
                  ],
                },
              },
              {
                $project: {
                  name: 1,
                  accountholderName: { $ifNull: ['$accountholderName', ''] },
                  routingNumber: 1,
                  accountNumber: '$accountNumber.display',
                  isManualEntry: 1,
                  paymentMethodType: 1,
                  'cardMetadata.accountId': 1,
                  'cardMetadata.type': 1,
                  billingAddress: 1,
                  isPrimary: 1,
                  accountType: 1,
                  finicity: 1,
                  status: 1,
                  _id: 1,
                  id: '$_id',
                },
              },
            ],
          },
        })
      } else {
        pipeline.push({
          $project: {
            'customer.bankAccounts': 0,
          },
        })
      }

      if (status) {
        if (status.includes('EXCEPT_')) {
          pipeline.push({
            $match: {
              $expr: {
                $ne: ['$status', status.split('_')[1]],
              },
            },
          })
        } else {
          if (status === invoiceStatus.seen) {
            pipeline.push({
              $match: {
                $and: [
                  {
                    $or: [
                      {
                        status: { $eq: invoiceStatus.seen },
                      },
                      {
                        status: { $eq: invoiceStatus.pastDue },
                      },
                    ],
                  },
                  {
                    seen: { $eq: true },
                  },
                ],
              },
            })
          } else if (status === invoiceStatus.placed) {
            pipeline.push({
              $match: {
                $and: [
                  {
                    status: { $eq: invoiceStatus.placed },
                  },
                  {
                    seen: { $eq: false },
                  },
                ],
              },
            })
          } else {
            pipeline.push({
              $match: {
                $expr: {
                  $eq: ['$status', status],
                },
              },
            })
          }
        }
      }

      if (search) {
        const searchMatchConditions: any[] = [
          {
            invoice_number: {
              $regex: search,
              $options: 'i',
            },
          },
          {
            'customer.name': {
              $regex: search,
              $options: 'i',
            },
          },
          {
            'customer.contact': {
              $regex: search,
              $options: 'i',
            },
          },
        ]
        if (moment(search as string, 'MM/DD/YYYY').isValid()) {
          searchMatchConditions.push({
            invoice_date: {
              $eq: search,
            },
          })
        }
        if (!isNaN(Number(search))) {
          const amount = parseFloat(search as string)
          searchMatchConditions.push({
            total_amount: {
              $eq: amount,
            },
          })
        }
        pipeline.push({
          $match: {
            $or: searchMatchConditions,
          },
        })
      }

      const columnsMap = {
        total_amount: 'total_amount',
        customer_name: 'customer.name',
        'customer.name': 'customer.name',
        type: 'type',
        invoice_number: 'invoice_number',
        status: 'sortable_status',
        invoice_due_date: 'invoice_due_date',
        invoice_date: 'invoice_date_to_sort',
        ar_advance_status: 'paymentDetails.arAdvanceStatus',
        authorization_deadline: 'quoteDetails.authorization_deadline',
        createdAt: 'createdAt',
      }

      const column =
        columnsMap[sortColumn as keyof typeof columnsMap] ||
        columnsMap.createdAt

      if (sortColumn === 'status') {
        pipeline.push({
          $addFields: {
            sortable_status: { $concat: ['$status', { $toString: '$seen' }] },
          },
        })
      }
      pipeline.push(
        {
          $sort: {
            [column]: (sortDirection || 'desc') === 'desc' ? -1 : 1,
          },
        },
        { $unset: 'sortable_status' },
      )

      const { paginationPipeline } = createPagination(req.query)

      const [{ invoices, totalCount }] = await Invoice.aggregate([
        ...pipeline,
        {
          $facet: {
            totalCount: [{ $count: 'count' }],
            invoices: [...paginationPipeline],
          },
        },
      ]).collation({
        locale: 'en',
        caseLevel: true,
      })

      const customerIds = invoices
        .map((i: any) => i.customer_account_id)
        .filter(Boolean)

      const billingContacts = await CompanyService.getMultiBillingContacts(
        customerIds,
      )

      const items = await Promise.all(
        invoices.map(async (i: any) => {
          return {
            ...i,
            id: i._id,
            customer: i.customer
              ? {
                  ...i.customer,
                  id: i.customer._id,
                  contacts: billingContacts[i.customer_account_id] || [],
                }
              : null,
          }
        }),
      )

      res.send({
        items,
        totalCount: totalCount?.[0]?.count ?? 0,
      })
    }
  },

  post: async (req, res) => {
    const { body, company, user, headers } = req
    const id = await invoicesService.saveInvoice(
      body,
      company!,
      user!,
      headers.origin!,
      false,
      req.session,
    )
    res.send({ id, result: 'ok' })
  },
} as ControllerItem
