import mongoose from 'mongoose'
import { exceptions } from '@linqpal/models'
import timestampsPlugin from '../helpers/timestampPlugin'
import { sleep } from '@linqpal/models/src/helpers/utils'
import { GlobalLogger } from './logger/logger.service'

mongoose.plugin(timestampsPlugin, { deduplicate: true })
mongoose.Promise = global.Promise
mongoose.set('sanitizeFilter', true)

let connection: mongoose.Connection

export const connectToDatabase = async (uri = '') => {
  if (connection && connection.readyState) {
    GlobalLogger.info(`=> using existing database connection`)
    return Promise.resolve()
  }

  GlobalLogger.info(`=> using new database connection`)
  const path = uri || process.env.LP_DB
  if (process.env.NODE_ENV === 'development') {
    GlobalLogger.debug(path)
  }

  if (!path)
    throw new exceptions.SystemError('unknown', 'Database link not provided')
  let trial = 0
  while (trial <= 3) {
    trial += 1
    try {
      mongoose.set('strictQuery', false)
      const db = await mongoose.connect(path, {
        serverSelectionTimeoutMS: 8000,
        bufferCommands: false,
        maxPoolSize: 1,
        socketTimeoutMS: 60000,
      })
      //mongoose.set('debug', true)
      GlobalLogger.info(`=> DB Connected! ${db.connections[0].readyState}`)
      connection = db.connections[0]
      return db
    } catch (error) {
      GlobalLogger.error(error)
    }
    await sleep(200)
  }
}
