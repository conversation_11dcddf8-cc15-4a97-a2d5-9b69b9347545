import { TFrontendRoute, TMiddlewares } from '@linqpal/models/src/routes2/types'
import { routes2 } from '@linqpal/models'
import { authMiddleware } from '../middlewares'
import { getDraft } from './getDraft'
import {
  IGetApplicationRequest,
  IGetApplicationResponse,
} from '@linqpal/models/src/routes2/application/types'

export default class Application extends routes2.classes.Application {
  middlewares: TMiddlewares = authMiddleware

  getDraft = getDraft as TFrontendRoute<
    IGetApplicationRequest,
    IGetApplicationResponse
  >
}
