import {
  exceptions,
  IEncrypted,
  INormalizedApplicationDraft,
  IOwnerInfo,
} from '@linqpal/models'
import { LexisService, User, UserRole } from '@linqpal/common-backend'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { decrypt } from '../linqpal/decisionEngine/wrapper'
import md5 from 'crypto-js/md5'
import { IUser } from '@linqpal/common-backend/src/models/types'
import moment from 'moment/moment'
import { parsePhoneNumber } from 'libphonenumber-js'
import { States, statesHashMap } from '@linqpal/models/src/dictionaries'
import { IEmailAge } from '@linqpal/common-backend/src/models/lexis-nexis.model/types'

interface IFraudRequest {
  body: IEmailAge
  ownerId?: string
  isPrincipal?: boolean
  ssn: string | IEncrypted
}

export async function getApplicationFraudData(
  draft: INormalizedApplicationDraft,
  company_id: string,
) {
  const userRole = await UserRole.findOne({ company_id, role: 'Owner' })
  const user = await User.findOne({ sub: userRole?.sub })

  const coOwners = draft.owners?.filter(
    (owner) => owner.type === OwnerTypes.INDIVIDUAL,
  )

  const requests: IFraudRequest[] = [
    createOwnerRequest(draft, user),
    ...coOwners.map((owner) => createCoOwnerRequest(owner, user)),
  ]

  const responses = await Promise.all(
    requests.map(async (request) => {
      const ssn = await decrypt(request.ssn)

      const [emailAge, fraudPoint] = await Promise.all([
        LexisService.emailAge(ssn, request.body),
        LexisService.fraudPoint(ssn, {
          ...request.body,
          nationalIdType: 'US_SSN',
          nationalIdNumber: ssn,
        }),
      ])

      return {
        owner: {
          id: request.ownerId,
          firstName: request.body.accountFirstName,
          lastName: request.body.accountLastName,
          type: OwnerTypes.INDIVIDUAL,
          isPrincipal: request.isPrincipal,
          key: md5(ssn).toString(),
        },
        emailAge: emailAge,
        fraudPoint: fraudPoint,
      }
    }),
  )

  return { fraud: responses }
}

function createOwnerRequest(
  draft: INormalizedApplicationDraft,
  user?: IUser | null,
): IFraudRequest {
  if (!draft.businessOwner_ssn) {
    throw new exceptions.LogicalError(
      `SSN is not provided for ${draft.businessOwner_firstName} ${draft.businessOwner_lastName}`,
    )
  }

  const birthday = moment(draft.businessOwner_birthdate, 'MM/DD/YYYY')
  return {
    body: {
      accountFirstName: draft.businessOwner_firstName,
      accountLastName: draft.businessOwner_lastName,
      accountDateOfBirth: birthday.format('YYYYMMDD'),
      accountTelephone: parsePhoneNumber(
        draft.businessOwner_phone ||
          draft.businessInfo_businessPhone ||
          user?.phone ||
          '',
        'US',
      ).nationalNumber.toString(),
      accountAddressStreet1: draft.businessOwner_address?.address || '',
      accountAddressCity: draft.businessOwner_address?.city || '',
      accountAddressState:
        statesHashMap[draft.businessOwner_address?.state as States],
      accountAddressZip: draft.businessOwner_address?.zip || '',
      accountEmail: draft.businessOwner_email || user?.email || '',
      inputIpAddress: user?.settings?.ip || undefined,
    },
    ownerId: draft.businessOwner_id,
    isPrincipal: true,
    ssn: draft.businessOwner_ssn,
  }
}

function createCoOwnerRequest(
  owner: IOwnerInfo,
  user?: IUser | null,
): IFraudRequest {
  if (!owner.ssn) {
    throw new exceptions.LogicalError(
      `SSN is not provided for ${owner.firstName} ${owner.lastName}`,
    )
  }

  const birthday = moment(owner.birthday, 'MM/DD/YYYY')
  return {
    body: {
      accountFirstName: owner.firstName,
      accountLastName: owner.lastName,
      accountDateOfBirth: birthday.format('YYYYMMDD'),
      accountTelephone: parsePhoneNumber(
        owner.phone,
        'US',
      ).nationalNumber.toString(),
      accountAddressStreet1: owner.address,
      accountAddressCity: owner.city,
      accountAddressState: statesHashMap[owner.state as States],
      accountAddressZip: owner.zip,
      accountEmail: owner.email,
      inputIpAddress: user?.settings?.ip || undefined,
    },
    ownerId: owner.id,
    ssn: owner.ssn,
  }
}
