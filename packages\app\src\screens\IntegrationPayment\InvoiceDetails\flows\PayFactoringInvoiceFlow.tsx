/* eslint-disable @typescript-eslint/no-shadow */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { routes, routes2 } from '@linqpal/models'
import { useTranslation } from 'react-i18next'
import { PayWithBankAccountFlow_ERROR } from '../components/PayWithBankAccountFlow_ERROR'
import { titleText } from '../utils/paymentModalTexts'
import {
  IntegrationErrorCodesType,
  PAYMENT_METHOD_TYPE,
  unexpectedErrorCode,
} from '@linqpal/models/src/dictionaries'
import { handleError } from '../helpers/getErrorMetadata'
import { PayWithBankAccountFlow_PROCESSING } from '../components/PayWithBankAccountFlow_PROCESSING'
import { PricingPackageResponse } from '@linqpal/models/src/routes2/invoice/types'
import { PayWithCreditCardFlow_PROCESSING } from '../components/PayWithCreditCardFlow_PROCESSING'
import { PayWithCreditCardFlow_ERROR } from '../components/PayWithCreditCardFlow_ERROR'
import { getPaymentMethodMetadata } from '../helpers/getPaymentMethodMetadata'
import { PayFactoringInvoiceModal } from '../components/PayFacoringInvoiceModal'
import { IInvoiceTotal } from '../components/PayFacoringInvoiceModal/types'
import { getCardFee } from '@linqpal/models/src/helpers/getCardFee'

enum PAYMENT_FLOW_STATE {
  IDLE = 'IDLE',
  PROCESSING = 'PROCESSING',
  ERROR = 'ERROR',
}

interface PayFactoringInvoiceFlowProps {
  invoiceId: string | null
  invoiceCompanyName: string
  paymentMethod: any
  jwtToken?: string
  invoice?: any
  onSuccess: () => void
  onClose: () => void
  onFail: () => void
  handleChangePaymentMethod: () => void
  handleAddNewPaymentMethod: () => void
}

const PaymentFlowComponents = {
  [PAYMENT_METHOD_TYPE.CARD]: {
    Processing: PayWithCreditCardFlow_PROCESSING,
    Error: PayWithCreditCardFlow_ERROR,
  },
  [PAYMENT_METHOD_TYPE.BANK]: {
    Processing: PayWithBankAccountFlow_PROCESSING,
    Error: PayWithBankAccountFlow_ERROR,
  },
}

export const PayFactoringInvoiceFlow: React.FC<PayFactoringInvoiceFlowProps> =
  ({
    invoiceId,
    invoiceCompanyName,
    paymentMethod,
    jwtToken,
    invoice,
    onSuccess,
    onClose,
    onFail,
    handleChangePaymentMethod,
    handleAddNewPaymentMethod,
  }) => {
    const [flowState, setFlowState] = useState<PAYMENT_FLOW_STATE>(
      PAYMENT_FLOW_STATE.IDLE,
    )
    const [loading, setLoading] = useState<boolean>(false)
    const [errorCode, setErrorCode] =
      useState<null | IntegrationErrorCodesType>(null)
    const [paymentState, setPaymentState] = useState<IInvoiceTotal>()
    const [calculatedDetails, setCalculatedDetails] = useState<IInvoiceTotal>()

    const [status, setStatus] = useState('')
    const [message, setMessage] = useState('')

    const { t } = useTranslation(['global', 'errors'])

    const errorCodeRef = useRef(errorCode)

    useEffect(() => {
      errorCodeRef.current = errorCode
    }, [errorCode])

    const getPricingPackageFee = useCallback(async () => {
      setLoading(true)
      try {
        let pricingPackageResponse: PricingPackageResponse | null = null

        if (invoiceId) {
          // TODO: VK: Unify calculations here and in ModalContent (move everything to PaymentTypeSelector which would calculate amount / fees)
          // full payment is a partial payment with full amount, so calculations can be unified
          pricingPackageResponse = await routes2.invoice.getPricingPackageFee({
            invoiceIds: [invoiceId],
            accountId: paymentMethod.id,
          })
        } else {
          if (invoice) {
            pricingPackageResponse =
              await routes2.invoice.getPricingPackageForIntegration({
                accountId: paymentMethod.id,
                companyId: invoice.company?.id,
                totalAmount: invoice.total_amount,
              })
            // customerFeeResponse remains null since we don't have invoiceId
          }
        }

        if (pricingPackageResponse?.pricingPackage) {
          const { discount, invoiceAmount } =
            pricingPackageResponse.pricingPackage

          const lateFee = pricingPackageResponse.lateFee ?? 0
          const customerFee = pricingPackageResponse?.customerFee ?? 0
          const totalAmount =
            (invoiceAmount || 0) + lateFee + customerFee - (discount || 0)

          let processingFee = 0

          if (paymentMethod.paymentMethodType === PAYMENT_METHOD_TYPE.CARD) {
            const cardFee = getCardFee({
              cardNetwork: paymentMethod?.cardMetadata?.network,
              cardType: paymentMethod?.cardMetadata?.type,
              isRegulated: paymentMethod?.cardMetadata?.isRegulated,
            })

            processingFee = (totalAmount * cardFee) / 100
          } else {
            const feeDetails =
              pricingPackageResponse.pricingPackage.pricingPackage.customer
            processingFee =
              (totalAmount * feeDetails.percentage) / 100 + feeDetails.amount
          }

          setPaymentState({
            ...pricingPackageResponse.pricingPackage,
            fee: processingFee,
            lateFee,
            customerFee,
            invoiceAmount: invoiceAmount ?? 0,
            totalAmount: totalAmount,
            totalAmountWithFee: totalAmount + processingFee,
          })
        }
      } catch (err) {
        console.error(err)
      } finally {
        setLoading(false)
      }
    }, [invoiceId, paymentMethod, invoice])

    useEffect(() => {
      getPricingPackageFee()
    }, [getPricingPackageFee])

    const handleNext = useCallback(
      async (calculatedInvoiceDetails?: IInvoiceTotal) => {
        if (flowState === PAYMENT_FLOW_STATE.IDLE) {
          setLoading(true)
          setCalculatedDetails(calculatedInvoiceDetails)
          try {
            let invoiceIdForPay = invoiceId

            if (!invoiceIdForPay) {
              if (!jwtToken) {
                setErrorCode(unexpectedErrorCode)
                throw new Error('JWT token is missing')
              }
              const { invoiceId: newInvoiceId, errorCode: error } =
                await routes.integration.upsertInvoiceFromJwt(jwtToken)

              if (error) {
                setErrorCode(error)
                throw new Error('Error occurred while upserting invoice')
              }
              invoiceIdForPay = newInvoiceId
            }

            if (!invoiceIdForPay) {
              setErrorCode(unexpectedErrorCode)
              throw new Error('Invoice ID is missing')
            }

            const isCardPayment = paymentMethod.paymentMethodType === 'card'
            const accountIdValue = isCardPayment
              ? paymentMethod.cardMetadata.accountId
              : paymentMethod.id

            await routes2.invoice.pay({
              invoiceIds: [invoiceIdForPay],
              paymentMethod: isCardPayment ? 'card' : 'ach',
              accountId: accountIdValue,
              account_id: accountIdValue,
              requestedAmount: calculatedInvoiceDetails?.invoiceAmount,
            })

            await new Promise((resolve) => setTimeout(resolve, 5000))

            setFlowState(PAYMENT_FLOW_STATE.PROCESSING)
          } catch (err: any) {
            console.error(err)

            const { status, message } = handleError(
              errorCodeRef.current,
              err,
              t,
            )

            setStatus(status)
            setMessage(message)

            setFlowState(PAYMENT_FLOW_STATE.ERROR)
          }
          setLoading(false)
        }
      },
      [flowState, invoiceId, paymentMethod, t, jwtToken],
    )

    const handleClose = useCallback(() => {
      onClose()
    }, [onClose])

    const { name: paymentMethodName, subtitle: paymentMethodSubtitle } =
      useMemo(
        () => getPaymentMethodMetadata(paymentMethod, t),
        [paymentMethod, t],
      )

    const FlowComponents =
      PaymentFlowComponents[paymentMethod.paymentMethodType]
    const ProcessingComponent = FlowComponents?.Processing
    const ErrorComponent = FlowComponents?.Error

    return (
      <>
        {flowState === PAYMENT_FLOW_STATE.IDLE && (
          <PayFactoringInvoiceModal
            loading={loading}
            invoiceTotal={paymentState}
            invoice={invoice}
            onPay={handleNext}
            onClose={handleClose}
            paymentMethod={paymentMethod}
            handleChangePaymentMethod={handleChangePaymentMethod}
            handleAddNewPaymentMethod={handleAddNewPaymentMethod}
          />
        )}
        {flowState === PAYMENT_FLOW_STATE.PROCESSING && ProcessingComponent && (
          <ProcessingComponent
            invoiceTotalAmount={
              calculatedDetails?.totalAmount || paymentState?.totalAmount || 0
            }
            invoiceCompanyName={invoiceCompanyName}
            paymentMethodName={paymentMethodName}
            subtitle={paymentMethodSubtitle}
            onClose={() => {
              handleClose()
              onSuccess?.()
            }}
          />
        )}
        {flowState === PAYMENT_FLOW_STATE.ERROR && ErrorComponent && (
          <ErrorComponent
            title={errorCode ? status : titleText(status)}
            subtitle={message}
            paymentMethodSubtitle={paymentMethodSubtitle}
            paymentMethodName={paymentMethodName}
            onClose={() => {
              handleClose()
              onFail?.()
            }}
            onContinue={handleClose}
          />
        )}
      </>
    )
  }
