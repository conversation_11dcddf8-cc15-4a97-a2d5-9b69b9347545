import { TCompoundRoute } from '@linqpal/models/src/routes2/types'
import middlewares from './middlewares'
import {
  Company,
  CustomerAccount,
  Invoice,
  invoicesService,
  LoanApplication,
  User,
  UserRole,
} from '@linqpal/common-backend'
import { PipelineStage, Types } from 'mongoose'
import { exceptions } from '@linqpal/models'
import transactional from '../../../services/transactional.service'
import {
  ICompany,
  ILoanApplication,
} from '@linqpal/common-backend/src/models/types'
import { linkInvoiceToCustomer } from '@linqpal/common-backend/src/services/invoices.service/linkInvoiceToCustomer'
import { parsePhoneNumber } from 'libphonenumber-js'

const format = '%m/%d/%Y'
const timezone = 'America/Chicago'

export const linkInvoice: TCompoundRoute<{ invoiceId: string }, any> = {
  middlewares: {
    pre: [...middlewares.pre, ...transactional.pre],
    post: transactional.post,
  },
  post: async (data, req) => {
    const { invoiceId } = data
    const userId = req.user._id.toString()
    const invoice = await Invoice.findById(invoiceId)
    if (!invoice) {
      return
    }
    await linkInvoiceToCustomer(invoice, userId, false, req.session)
    await req.session.commitTransaction()
  },
}

export const checkInvoiceIsPaid: TCompoundRoute<{ invoiceId: string }, any> = {
  middlewares: {
    pre: [],
  },
  get: async (data) => {
    const { invoiceId } = data

    const result = await invoicesService.checkInvoiceIsPaid(invoiceId)
    return result
  },
}

export const checkInvoiceIsCanceled: TCompoundRoute<
  { invoiceId: string },
  { isCanceled: boolean }
> = {
  middlewares: {
    pre: [],
  },
  get: async (data) => {
    const { invoiceId } = data

    const result = await invoicesService.checkInvoiceIsCanceled(invoiceId)
    return result
  },
}

export const showInvoiceIdle: TCompoundRoute<
  { invoiceId?: string; invoiceNumber?: string; supplierCompanyId?: string },
  any
> = {
  middlewares: {
    pre: [],
  },
  get: async (data, req) => {
    const { invoiceId, invoiceNumber, supplierCompanyId } = data

    const invoice = await getInvoiceIdle({
      invoiceId,
      invoiceNumber,
      supplierCompanyId,
    })
    await Invoice.findByIdAndUpdate(invoiceId, { seen: true })

    if (!invoice) {
      return null
    }

    if (invoice.company_id && !invoice.company) {
      throw new exceptions.LogicalError('Company not found')
    }

    const invoice_id = invoice._id.toString()

    const customer = invoice.customer_account_id
      ? await CustomerAccount.findById(
          { _id: invoice.customer_account_id },
          { bankAccounts: 0 },
        )
      : null
    const payerInfo = invoice.payersInfo?.length ? invoice.payersInfo[0] : null

    if (payerInfo?.cellPhoneNumber) {
      try {
        const payerPhone = parsePhoneNumber(
          payerInfo.cellPhoneNumber,
          'US',
        ).number.toString()
        payerInfo.cellPhoneNumber = payerPhone
      } catch (error) {
        console.error('Error parsing phone number:', error)
        payerInfo.cellPhoneNumber = ''
      }
    }

    if (customer) {
      customer.user = await findUserByLogins([customer.phone, customer.email])
    } else if (payerInfo) {
      payerInfo.user = await findUserByLogins([
        payerInfo.cellPhoneNumber,
        payerInfo.emailAddress,
      ])
    }

    let loanApplications = new Array<ILoanApplication>()
    let companyForBankAcc: ICompany | null | undefined
    if (customer?.user) {
      const customerRole = await UserRole.findOne({ sub: customer.user.sub })
      companyForBankAcc = await Company.findById(customerRole?.company_id)
    } else if (invoice.company_id === '') {
      companyForBankAcc = await Company.findById(invoice.payer_id)
    }
    if (companyForBankAcc) {
      loanApplications = await LoanApplication.aggregate([
        { $match: { company_id: companyForBankAcc._id.toString() } },
        {
          $match: {
            $expr: {
              $cond: {
                if: { $isArray: '$invoiceDetails.invoiceId' },
                then: { $in: [invoice_id, '$invoiceDetails.invoiceId'] },
                else: { $eq: ['$invoiceDetails.invoiceId', invoice_id] },
              },
            },
          },
        },
        { $sort: { createdAt: -1, updatedAt: -1 } },
      ])
    }

    const currentInvoiceCreditStatus: string | undefined =
      loanApplications.find((l) => {
        return Array.isArray(l.invoiceDetails?.invoiceId)
          ? l.invoiceDetails?.invoiceId.includes(invoice_id)
          : [l.invoiceDetails?.invoiceId].includes(invoice_id)
      })?.status

    invoice.company.bnplPaymentAvailable =
      invoice.company.settings?.loanPricingPackageId !== 'optOut'

    invoice.company.cardPaymentAvailable =
      invoice.company.settings?.cardPricingPackageId !== 'optOut'

    invoice.company.acceptAchPayment =
      invoice.company.settings?.acceptAchPayment !== false &&
      customer?.settings?.acceptAchPayment !== false

    const predictedCustomer = customer
      ? {
          email: customer?.email,
          phone: customer?.phone,
          firstName: customer?.first_name,
          lastName: customer?.last_name,
          businessName: customer?.name,
          isAuthorized: !!customer?.user && !customer.user.isGuest,
        }
      : payerInfo
      ? {
          email: payerInfo?.emailAddress,
          phone: payerInfo?.cellPhoneNumber,
          firstName: payerInfo?.firstName,
          lastName: payerInfo?.lastName,
          businessName: payerInfo?.businessName,
          isAuthorized: !!payerInfo?.user && !payerInfo.user.isGuest,
        }
      : null

    return {
      invoice,
      currentInvoiceCreditStatus,
      customer: predictedCustomer,
    }
  },
}

async function getInvoiceIdle(params: {
  invoiceId?: string
  invoiceNumber?: string
  supplierCompanyId?: string
}) {
  const { invoiceId, invoiceNumber, supplierCompanyId } = params

  if (!invoiceId && !(invoiceNumber && supplierCompanyId)) {
    return null
  }

  const matchCondition: Record<string, any> = {}
  if (invoiceId) matchCondition._id = new Types.ObjectId(invoiceId)
  if (invoiceNumber) matchCondition.invoice_number = invoiceNumber
  if (supplierCompanyId) matchCondition.company_id = supplierCompanyId

  const pipeline: PipelineStage[] = [
    { $match: matchCondition },
    {
      $addFields: {
        supplier_id: {
          $convert: {
            input: '$company_id',
            to: 'objectId',
            onError: null,
          },
        },
      },
    },
    {
      $lookup: {
        from: Company.collection.name,
        as: 'company',
        localField: 'supplier_id',
        foreignField: '_id',
      },
    },
    { $unwind: { path: '$company', preserveNullAndEmptyArrays: true } },
    { $unset: ['company.bankAccounts'] },
    {
      $project: {
        _id: 1,
        company_id: 1,
        company: {
          name: { $ifNull: ['$company.name', null] },
          phone: { $ifNull: ['$company.phone', null] },
          settings: {
            acceptAchPayment: 1,
            cardPricingPackageId: 1,
            loanPricingPackageId: 1,
          },
        },
        customer_account_id: 1,
        invoice_due_date: {
          $dateToString: { date: '$invoice_due_date', format, timezone },
        },
        invoice_number: 1,
        total_amount: 1,
        connector: 1,
        createdAt: 1,
        payer_id: 1,
        type: 1,
        supplierInvitationDetails: 1,
        payersInfo: 1,
        addressType: 1,
        address: 1,
        attention: 1,
        invoice_document: 1,
      },
    },
  ]

  const invoice = await Invoice.aggregate(pipeline)
  const result = invoice.length > 0 && {
    ...invoice[0],
    achDiscount: (invoice[0].total_amount * invoice[0].achDiscount) / 100,
  }
  return invoice.length > 0 ? result : undefined
}

function findUserByLogins(logins: (string | undefined)[]) {
  const validLogins = logins.filter(Boolean) // remove empty strings

  if (validLogins.length) {
    return User.findOne({
      $or: validLogins.map((login) => ({ login })),
    })
  }

  return null
}
