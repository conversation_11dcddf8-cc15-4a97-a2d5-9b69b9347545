import React from 'react'
import {
  ArrowBlue,
  BackHeader,
  BankIcon,
  CardIconBuilder,
  ChevronDownIcon,
  ChevronUpIcon,
  NewAttchmentIcon,
  SparkIcon,
} from '../../../../assets/icons'
import { ListItem } from '../../../../ui/atoms/builder-2.0/ListItem'
import { StyleSheet, Text, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { AddPaymentMethodItems } from '../../../../ui/add-payment-method-components/AddPaymentMethodItems'
import { Hr, TextWithHorizontalLine } from '../../../../ui/molecules'
import { TouchableOpacity } from 'react-native-gesture-handler'
import { HeaderButton } from '../../../../ui/molecules/Layout'
import { GetIcon } from '../../../../ui/atoms/GetIcon'
import { paths } from '../../../links'
import { useResponsive } from '../../../../utils/hooks'
import { Spacer } from '../../../../ui/atoms'
import { BtText } from '@linqpal/components/src/ui/BtText'
import { HEADER_HEIGHT } from '../../MoreTab'
import { useStore } from '../../../../store'
import { PaymentMethod } from './InvoiceDetails'
import { Divider } from 'react-native-paper'
import { BButton } from '../../../../ui/atoms/builder-2.0/Button'
import { EInvoiceType } from '@linqpal/models'

interface AddPaymentMethodOptionsProps {
  id?: string
  onNavigate?: () => void
  navigation: any
  getBankAccounts: () => void
  canAddBankAccount?: boolean
}

interface PaymentMethodItemProps {
  item: PaymentMethod
  onPaymentSelection: (arg: PaymentMethod) => void
  disabled?: boolean
  canPay?: boolean // for quotes phase #1 display payment methods but forbid to pay
}

export const AddPaymentMethodOptions: React.FC<AddPaymentMethodOptionsProps> =
  ({
    id,
    onNavigate,
    navigation,
    getBankAccounts,
    canAddBankAccount = true,
  }) => {
    return (
      <AddPaymentMethodItems
        sourceInvoice={id}
        canAddBankAccount={canAddBankAccount}
        navigation={navigation}
        onNavigate={onNavigate}
        goToNextOnboardingPage={getBankAccounts}
        itemStyle={{
          otherStyles: { title: { lineHeight: 24 } },
          style: styles.listItem,
          frontIcon: { height: 40, width: 40 },
        }}
        testID="addPaymentMethods"
      />
    )
  }

export const OrDivider = ({ showText = true }) => {
  const { t } = useTranslation('global')
  return showText ? (
    <TextWithHorizontalLine
      color="#CCD6DD"
      containerStyle={{ marginBottom: 20 }}
    >
      <Text style={styles.orText}>{t('InvoiceDetails.or')}</Text>
    </TextWithHorizontalLine>
  ) : (
    <View style={{ marginBottom: 20 }}>
      <Hr color={'#CCD6DD'} />
    </View>
  )
}

export const CollapseIcon = ({ collapsed, onPress }) => {
  return (
    <TouchableOpacity style={{ padding: 5 }} onPress={onPress}>
      {collapsed ? (
        <ChevronDownIcon fill={'#003353'} width={15} height={15} />
      ) : (
        <ChevronUpIcon fill={'#003353'} width={15} height={15} />
      )}
    </TouchableOpacity>
  )
}
export const InvoicesHeader = ({ inv, navigation, isUserAuthenticated }) => {
  const { t } = useTranslation('global')

  const onBack = () => {
    if (navigation.canGoBack()) {
      navigation.goBack()
    } else {
      navigation.replace(paths.Home._self, {
        screen: paths.Console.Payables._self,
        params: {
          screen: paths.Console.Payables.home,
        },
      })
    }
  }

  const onInvoiceDocClick = () => {
    navigation.navigate(paths.Console.Payables.AttachmentPreview, {
      id: inv._id,
    })
  }

  return (
    <View style={styles.header}>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <HeaderButton
          icon={
            isUserAuthenticated ? (
              <GetIcon
                icon={<BackHeader height="20" width="20" />}
                onPress={onBack}
              />
            ) : (
              ''
            )
          }
          onPress={() => {}}
        />
      </View>
      <Text style={{ fontWeight: '600', fontSize: 18 }}>
        {t('InvoiceDetails.title')}
      </Text>
      {inv.invoice_document ? (
        <GetIcon
          icon={<NewAttchmentIcon height="20" width="20" />}
          onPress={onInvoiceDocClick}
        />
      ) : (
        <View style={{ width: 50 }} />
      )}
    </View>
  )
}

export const GroupedInvoicesHeader = ({ navigation }) => {
  const { screenWidth } = useResponsive()
  const { t } = useTranslation('global')
  const wideScreen = screenWidth > 450
  return (
    <View style={[styles.header, { marginBottom: wideScreen ? 20 : 0 }]}>
      <GetIcon
        icon={<BackHeader height="20" width="20" />}
        onPress={() => {
          if (navigation.canGoBack()) {
            navigation.goBack()
          } else {
            navigation.replace(paths.Home._self, {
              screen: paths.Console.Payables._self,
              params: {
                screen: paths.Console.Payables.home,
              },
            })
          }
        }}
      />
      <BtText
        style={{
          fontWeight: '600',
          fontSize: 18,
          marginLeft: 10,
        }}
      >
        {t('InvoiceDetails.title')}
      </BtText>
      <Spacer width={40} />
    </View>
  )
}

export const PaymentMethodItem: React.FC<PaymentMethodItemProps> = ({
  item,
  onPaymentSelection,
  canPay = true,
  disabled = false,
  ...rest
}) => {
  const { t } = useTranslation('global')
  const {
    screensStore: { paymentMethodsStore },
  } = useStore()
  return (
    <ListItem
      icon={
        item.paymentMethodType === 'card' ? <CardIconBuilder /> : <BankIcon />
      }
      style={styles.listItem}
      testID={'payment-method-of-' + `${item.paymentMethodType}`}
      title={item.name?.split('/')?.[0]}
      subtitle={paymentMethodsStore.getPaymentMethodSubtitle(t, item)}
      endIcon={canPay ? <ArrowBlue style={{ height: 26, width: 26 }} /> : <></>}
      onPress={() => {
        if (canPay) onPaymentSelection(item)
      }}
      otherStyles={{
        title: {
          lineHeight: 20,
          fontWeight: '600',
          marginBottom: 2,
        },
        frontIcon: { marginTop: 14, width: 40, height: 40 },
      }}
      disabled={disabled}
      {...rest}
    />
  )
}

export const PayNowButton = ({ onPayNow, invoice }) => {
  const { t } = useTranslation('global')

  return (
    <View style={{ flexGrow: 1 }}>
      <Divider style={{ marginTop: 14 }} />
      <BButton
        testID="payNow"
        buttonStyle={{
          width: '100%',
          borderRadius: 4,
          marginTop: 50,
        }}
        labelStyle={{ fontWeight: '700' }}
        onPress={onPayNow}
      >
        {invoice?.type === EInvoiceType.QUOTE
          ? t('InvoiceDetails.view-payment-methods')
          : t('InvoiceDetails.pay-now')}
      </BButton>
    </View>
  )
}

export const EligibleForACHdiscount = ({ label, compactDesign = false }) => {
  return (
    <View
      style={
        compactDesign ? styles.compactAchDiscountView : styles.achDiscountView
      }
    >
      <SparkIcon style={{ marginRight: compactDesign ? 5 : 13 }} />
      <Text
        style={
          compactDesign ? styles.compactAchDiscountText : styles.achDiscountText
        }
      >
        {label}
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  listItem: {
    borderWidth: 1,
    borderColor: '#CCD6DD',
    height: 72,
    borderRadius: 8,
  },
  orText: {
    fontWeight: '400',
    fontSize: 12,
    color: '#99ADBA',
  },
  header: {
    height: HEADER_HEIGHT,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F7F8',
  },
  achDiscountView: {
    height: 48,
    width: '96%',
    borderRadius: 8,
    backgroundColor: 'rgba(0, 160, 243, 0.05)',
    paddingVertical: 14,
    paddingHorizontal: 18,
    flexDirection: 'row',
    marginBottom: 14,
    marginHorizontal: 9,
  },
  compactAchDiscountView: {
    height: 26,
    width: 300,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 160, 243, 0.05)',
    paddingVertical: 5,
    paddingHorizontal: 10,
    flexDirection: 'row',
    alignSelf: 'center',
  },
  compactAchDiscountText: {
    fontWeight: '700',
    fontSize: 12,
    lineHeight: 16,
    color: '#00A0F3',
  },
  achDiscountText: {
    fontWeight: '700',
    fontSize: 14,
    lineHeight: 21,
    color: '#00A0F3',
  },
})
