import React from 'react'
import { observer } from 'mobx-react'
import Wrapper from '../../../../Contractor/Wrapper'
import { TopBarMobileIdle } from '../../../InvoiceDetails/components/ScreenWrapper'
import { LoginButton } from '../../../InvoiceDetails/components/TopBarButtons'
import { InvoiceIsPaidInfo } from '../../../InvoiceDetails/components/InvoiceIsPaidInfo'
import { InvoicePaymentProcessingInfo } from '../../../InvoiceDetails/components/InvoicePaymentProcessingInfo'
import { dictionaries } from '@linqpal/models'
import { LOAN_APPLICATION_STATUS } from '@linqpal/models/src/dictionaries'
import { useTranslation } from 'react-i18next'
import { InvoicePanel } from '../../components/InvoicePanel'
import { StyleSheet, View } from 'react-native'
//import { Attachment } from './components/Attachment'
import { PayTermsAndConditionsForIntegration } from '../../../../Auth/TermsAndConditions'
import Button from '../../components/Button'
import { CompanyHeader_v2 } from '../../../InvoiceDetails/components/CompanyHeader'
import { OtherInvoiceInfo_v2 } from '../../../InvoiceDetails/components/OtherInvoiceInfo'
import { Divider } from 'react-native-paper'
import { useResponsive } from '@linqpal/components/src/hooks'
import { PoweredByBluetape } from '../../../../../ui/white-label-components/PoweredByBluetape'
import RootStore from '../../../../../store'

interface InvoiceDetailsIdle_v2Props {
  invoice: any
  invoicePaymentStatus: string | null
  invoiceCreditExist: boolean
  invoiceCreditStatus: string
  isInvoicePaid: boolean
  onPressLogin: () => void
  onPressSignUp: () => void
  onPressPayAsGuest: () => void
  isWeb: boolean
  customer: any
}

export const InvoiceDetailsIdle_v2: React.FC<InvoiceDetailsIdle_v2Props> =
  observer(
    ({
      invoice,
      invoiceCreditExist,
      isInvoicePaid,
      onPressLogin,
      onPressSignUp,
      onPressPayAsGuest,
      invoicePaymentStatus,
      invoiceCreditStatus,
      isWeb,
      customer,
    }) => {
      const { branding } = RootStore
      const { t } = useTranslation('global')
      const isInvoicePaidOff = isInvoicePaid || invoiceCreditExist
      const { mobileWidth } = useResponsive()

      const getDesktopButtons = () => {
        if (isInvoicePaidOff) {
          return [
            {
              label: t('integrations.buttons.pay-invoice'),
              onPress: () => {},
              disabled: true,
            },
          ]
        }

        if (customer?.isAuthorized) {
          return [
            {
              label: t('integrations.buttons.login-to-pay'),
              onPress: onPressLogin,
            },
          ]
        }

        if (!customer) {
          return [
            {
              label: t('integrations.buttons.login-to-pay'),
              onPress: onPressLogin,
            },
            {
              label: t('integrations.buttons.pay-by-card'),
              onPress: onPressPayAsGuest,
              style: {
                backgroundColor: '#FFFFFF',
              },
              textStyle: { color: '#001929' },
            },
          ]
        }

        return [
          {
            label: t('integrations.buttons.sign-up-and-pay'),
            onPress: onPressSignUp,
          },
          {
            label: t('integrations.buttons.pay-by-card'),
            onPress: onPressPayAsGuest,
            style: {
              backgroundColor: '#FFFFFF',
            },
            textStyle: { color: '#001929' },
          },
        ]
      }

      const getMobileButtons = () => {
        if (isInvoicePaidOff) return null

        if (customer?.isAuthorized) {
          return (
            <View
              style={{
                paddingHorizontal: 20,
                marginBottom: 27,
              }}
            >
              <Button
                label={t('integrations.buttons.login-and-pay')}
                onPress={onPressLogin}
                buttonStyle={{ height: 50 }}
                textStyle={{ fontSize: 14 }}
              />
            </View>
          )
        }

        if (!customer) {
          return (
            <View
              style={{
                paddingHorizontal: 20,
                height: 92,
                justifyContent: 'space-between',
                marginBottom: 27,
              }}
            >
              <Button
                label={t('integrations.buttons.login-to-pay')}
                onPress={onPressLogin}
                buttonStyle={{ height: 40 }}
                textStyle={{ fontSize: 14 }}
              />
              <Button
                label={t('integrations.buttons.pay-by-card')}
                onPress={onPressPayAsGuest}
                buttonStyle={{ backgroundColor: '#F8F9F9', height: 40 }}
                textStyle={{ color: '#001929', fontSize: 14 }}
              />
            </View>
          )
        }

        return (
          <View
            style={{
              paddingHorizontal: 20,
              height: 92,
              justifyContent: 'space-between',
              marginBottom: 27,
            }}
          >
            <Button
              label={t('integrations.buttons.sign-up-and-pay')}
              onPress={onPressSignUp}
              buttonStyle={{ height: 40 }}
              textStyle={{ fontSize: 14 }}
            />
            <Button
              label={t('integrations.buttons.pay-by-card')}
              onPress={onPressPayAsGuest}
              buttonStyle={{ backgroundColor: '#F8F9F9', height: 40 }}
              textStyle={{ color: '#001929', fontSize: 14 }}
            />
          </View>
        )
      }
      /*const additionalFields = [
        {
          label: t('integrations.invoice.attachment'),
          value: (
            <Attachment
              invoiceNumber={invoice.invoice_number}
              onPress={() => Linking.openURL('https://google.com')}
            />
          ),
        },
      ]*/

      return (
        <Wrapper
          containerStyle={{
            alignSelf: 'center',
          }}
          contentContainerStyle={{ height: '100%', paddingTop: isWeb ? 42 : 0 }}
          footer={
            <>
              {!isWeb && getMobileButtons()}
              {!!branding && isInvoicePaidOff && !isWeb && (
                <View style={styles.poweredByContainer}>
                  <PoweredByBluetape />
                </View>
              )}
            </>
          }
          styleWidth={!isWeb ? '100%' : 500}
          toolbar={
            !isWeb && (
              <TopBarMobileIdle
                button={
                  isInvoicePaidOff && (
                    <LoginButton isWeb={isWeb} onPress={onPressLogin} />
                  )
                }
              />
            )
          }
        >
          {isWeb ? (
            <InvoicePanel
              invoice={{
                company: {
                  name: invoice.company.name,
                  phone: invoice.company.phone,
                },
                total_amount: Number(invoice.total_amount),
                type: invoice.type,
                invoice_number: invoice.invoice_number,
                invoice_due_date: invoice.invoice_due_date,
              }}
              buttons={isWeb ? getDesktopButtons() : []}
              footer={
                isInvoicePaidOff ? (
                  <PayTermsAndConditionsForIntegration />
                ) : undefined
              }
              // eslint-disable-next-line i18next/no-literal-string
              backgroundColor="#F8F9F9"
            />
          ) : (
            <View
              style={[
                styles.container,
                { width: mobileWidth > 576 ? '100%' : mobileWidth - 40 },
              ]}
            >
              <CompanyHeader_v2
                name={invoice.company?.name || ''}
                phone={invoice.company?.phone || ''}
                totalAmount={Number(invoice.total_amount)}
                style={{
                  paddingVertical: 20,
                  flexDirection: 'row',
                }}
              />
              <Divider testID={'divider'} />
              <OtherInvoiceInfo_v2
                type={invoice.type}
                invoice_number={invoice.invoice_number}
                invoice_due_date={invoice.invoice_due_date}
              />
            </View>
          )}

          {((isInvoicePaid &&
            invoicePaymentStatus === dictionaries.OPERATION_STATUS.SUCCESS) ||
            invoiceCreditStatus === LOAN_APPLICATION_STATUS.CLOSED) && (
            <InvoiceIsPaidInfo isWeb={isWeb} />
          )}
          {((invoiceCreditExist &&
            invoiceCreditStatus !== LOAN_APPLICATION_STATUS.CLOSED) ||
            (isInvoicePaid &&
              invoicePaymentStatus ===
                dictionaries.OPERATION_STATUS.PROCESSING)) && (
            <InvoicePaymentProcessingInfo isWeb={isWeb} />
          )}
        </Wrapper>
      )
    },
  )

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 16,
    maxWidth: '100%',
    width: 350,
  },
  poweredByContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'flex-end',
    borderTopWidth: 1,
    borderTopColor: '#E6EBEE',

    paddingTop: 10,
    marginBottom: 18,
    paddingRight: 10,
  },
})
