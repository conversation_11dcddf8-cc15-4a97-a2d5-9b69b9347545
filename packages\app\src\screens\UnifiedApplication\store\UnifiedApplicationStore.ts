import { FlowController_v1 } from '../../GeneralApplication/Store/FlowController_v1'
import { UnifiedApplicationFlow } from '../../GeneralApplication/Store/UnifiedApplicationFlow'
import { routes2 } from '@linqpal/models'
import { UnifiedApplicationDraft } from './UniffiedApplicationDraft'
import { Steps } from '../../GeneralApplication/Store/ApplicationSteps'
import { IUnifiedApplicationOptions } from '@linqpal/models/src/types/unifiedApplication/IUnifiedApplicationOptions'
import RootStore from '../../../store/RootStore'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'

export class UnifiedApplicationStore {
  private _flow: UnifiedApplicationFlow = new UnifiedApplicationFlow()

  private _options: IUnifiedApplicationOptions

  private _document: UnifiedApplicationDraft

  private _flowController: FlowController_v1<
    UnifiedApplicationDraft,
    IUnifiedApplicationOptions
  >

  private _previousStep = ''

  constructor() {
    this._flow = new UnifiedApplicationFlow()
    this._document = new UnifiedApplicationDraft()
    this._flowController = new FlowController_v1(this._flow.flow)

    this._options = {
      // TODO: VK: Unified: review type
      type: ApplicationType.Credit,
      userId: RootStore.userStore.id,
    }
  }

  public get currentStep(): string {
    return this._document?.currentStep || ''
  }

  public get previousStep(): string {
    return this._previousStep
  }

  public get currentGroup(): string {
    const currentStep = this._document?.currentStep || ''
    const group = currentStep.split('.')[0] || ''

    return group
  }

  public get document() {
    return this._document
  }

  public get isCurrentStepValid(): boolean {
    return this.currentStep
      ? this._document.validate(this.currentStep, this._options)
      : false
  }

  public get isInReview(): boolean {
    return this.currentGroup === Steps.review._
  }

  public async loadDraft() {
    // TODO: VK: Unified: review type
    this._options = {
      type: ApplicationType.Credit,
      userId: RootStore.userStore.id,
    }

    const draftResponse = await routes2.application.getDraft({})

    if (draftResponse.draft) {
      this._document = new UnifiedApplicationDraft(draftResponse.draft)
    } else {
      // TODO: VK: calculate initial steps as in old ApplicationStore
      const initialStep = Steps.businessInfo.email.path

      this._document = new UnifiedApplicationDraft({
        initialStep,
        currentStep: initialStep,
        data: {
          businessInfo: {
            businessName: {},
          },
          finance: {},
          businessOwner: {},
          coOwnerInfo: {},
          bank: {},
          review: {},
        },
      })

      console.log('new unified application, initial step', initialStep)
    }

    this._flowController = new FlowController_v1(this._flow.flow)

    return Promise.resolve()
  }

  public async saveApplication() {
    // call on next / back / skip / close / submit. Enqueue save requests, check conflicts
    return Promise.resolve()
  }

  public goToStep(path: string) {
    this._previousStep = this.currentStep
    this._document.currentStep = path
  }

  public moveForward() {
    const newStep = this._flowController.getNextStep(
      this.currentStep,
      this._document,
      this._options,
    )

    this.setCurrentStep(newStep)
  }

  public moveBackward() {
    const newStep = this._flowController.getPreviousStep(
      this.currentStep,
      this._document,
      this._options,
    )
    console.log('=>(UnifiedApplicationStore.ts:129) newStep', newStep)
    this.setCurrentStep(newStep)
  }

  public skipStep() {
    const newStep = this._flowController.findSkipStep(
      this.currentStep,
      this._document,
      this._options,
    )
    console.log('=>(UnifiedApplicationStore.ts:139) newStep', newStep)

    this.setCurrentStep(newStep)
  }

  private setCurrentStep(step: string) {
    this._previousStep = this.currentStep
    this._document.currentStep = step
  }
}
