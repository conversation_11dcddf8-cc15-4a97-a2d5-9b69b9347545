import { CriticalError } from '@linqpal/models/src/types/exceptions'
import { DotNetInvoiceType } from '../services/invoice.dotnet.service'
import { EPaymentType } from '../services/payment/types'
import { EInvoiceType } from '@linqpal/models'
import { PayableType } from '../services/onBoarding/types'

export class DotNetMapper {
  static invoiceTypeToEPaymentType(type: DotNetInvoiceType) {
    switch (type) {
      case DotNetInvoiceType.Invoice:
        return EPaymentType.invoice
      case DotNetInvoiceType.Quote:
        return EPaymentType.quote
      case DotNetInvoiceType.SalesOrder:
        return EPaymentType.sales_order
      default:
        throw new CriticalError(
          'Unknown dotnet invoice type for mapping to EPaymentType enum',
          {
            type,
          },
        )
    }
  }

  static invoiceTypeToPayableType(type: EInvoiceType): PayableType {
    switch (type) {
      case EInvoiceType.INVOICE:
        return PayableType.Invoice
      case EInvoiceType.QUOTE:
        return PayableType.Quote
      case EInvoiceType.SALE_ORDER:
        return PayableType.SalesOrder
      default:
        throw new Error(`unable to map ${type} to PayableType`)
    }
  }
}
