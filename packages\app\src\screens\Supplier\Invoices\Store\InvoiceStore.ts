import { EInvoiceType, InvoicePaymentType, routes } from '@linqpal/models'
import { BaseTableStore } from '../../../../store/BaseTableStore'
import { currencyMask } from '../../../../utils/helpers/masking'
import { CancellablePromise } from '@linqpal/models/src/helpers/caller'
import { invoiceStatus } from '@linqpal/models/src/dictionaries'
import { IReceivablesFilter } from './Types/IReceivablesFilter'
import { IInvoiceTableItem } from './Types/IInvoiceTableItem'
import { TFunction } from 'react-i18next'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'
import {
  PaidInvoiceStatuses,
  PaymentProcessingInvoiceStatuses,
} from '../../../Contractor/PayablesTab/enums'

export class InvoiceStore extends BaseTableStore<
  IInvoiceTableItem,
  IReceivablesFilter
> {
  exportFileName = 'invoices'

  paymentType: string | null

  filter: IReceivablesFilter = {
    search: '',
    duration: 14,
    durationIndex: 0,
    status: 'EXCEPT_DRAFT',
    statusIndex: 0,
  }

  statuses = Object.keys(invoiceStatus)
    .filter(
      (status) =>
        ![
          invoiceStatus.draft,
          invoiceStatus.authorized,
          invoiceStatus.authorizationInReview,
          invoiceStatus.invoiced,
          invoiceStatus.paymentPosted,
        ].includes(invoiceStatus[status]),
    )
    .map((status) => invoiceStatus[status])

  t: TFunction<'global'> | undefined

  constructor(paymentType: string | null) {
    super()
    this.paymentType = paymentType
    this.initObservable()
  }

  callApi(params: any): CancellablePromise<any> {
    return routes.invoices.allInvoices({
      ...params,
      type: EInvoiceType.INVOICE,
      paymentType: this.paymentType,
    })
  }

  formatTableItem(invoice: any) {
    const customer = invoice.customer
    const contact = customer?.display_name
      ? customer.display_name
      : `${customer?.first_name || ''} ${customer?.last_name || ''}`.trim() ||
        'N/A'

    return {
      invoice_number: invoice.invoice_number,
      payersInfo: invoice.payersInfo?.length
        ? invoice.payersInfo.filter(Boolean)
        : null,
      contact,
      customer_name: invoice.customer.name,
      status: invoice.status,
      ar_advance_status:
        invoice.paymentDetails?.arAdvanceStatus ?? ArAdvanceStatus.NotApplied,
      payment_type:
        invoice.paymentDetails?.paymentType ?? InvoicePaymentType.PAYNOW,
      invoice_date: invoice.invoice_date,
      invoice_due_date: invoice.invoice_due_date,
      total_amount: currencyMask(invoice.total_amount),
      totalRemainingAmount: invoice.totalRemainingAmount,
      totalPaidAmount: invoice.totalPaidAmount,
      totalProcessingAmount: invoice.totalProcessingAmount,
      lateFee: invoice.lateFee,
      invoice,
    }
  }

  formatExportItem(
    tableItem: IInvoiceTableItem,
  ): { [key: string]: string } | null {
    if (!this.t) {
      throw new Error('call setTranslation() first')
    }
    if (
      this.paymentType &&
      tableItem.payment_type === InvoicePaymentType.PAYNOW
    ) {
      return null
    }

    const paidAmount = tableItem.totalPaidAmount
      ? currencyMask(tableItem.totalPaidAmount)
      : PaidInvoiceStatuses.includes(tableItem.status)
      ? tableItem.total_amount
      : currencyMask(0)

    const processingAmount = tableItem.totalProcessingAmount
      ? currencyMask(tableItem.totalProcessingAmount)
      : PaymentProcessingInvoiceStatuses.includes(tableItem.status)
      ? tableItem.total_amount
      : currencyMask(0)

    const remainingAmount =
      PaymentProcessingInvoiceStatuses.includes(tableItem.status) ||
      PaidInvoiceStatuses.includes(tableItem.status)
        ? currencyMask(0)
        : currencyMask(tableItem.totalRemainingAmount)

    return {
      [this.t('Receivables.invoice.number-export')]: tableItem.invoice_number,
      [this.t('Receivables.contact')]: tableItem.contact,
      ...(!this.paymentType && {
        [this.t('Receivables.payers-info-recipients')]: tableItem.payersInfo
          ?.length
          ? tableItem.payersInfo.filter(Boolean).join(', ')
          : '-',
      }),

      [this.t('Receivables.business')]: tableItem.customer_name,
      [this.t('Receivables.invoice-status')]: tableItem.status,
      ...(this.paymentType && {
        [this.t('Receivables.ar-advance.status-column')]:
          tableItem.ar_advance_status,
      }),
      [this.t('Receivables.due-date')]: tableItem.invoice_due_date,
      [this.t('Receivables.invoice-amount')]: tableItem.total_amount,
      [this.t('Receivables.late-fee')]: currencyMask(tableItem.lateFee),
      [this.t('Receivables.paid-amount')]: paidAmount,
      [this.t('Receivables.processing-amount')]: processingAmount,
      [this.t('Receivables.remaining-amount')]: remainingAmount,
    }
  }

  setTranslation(t: TFunction<'global'>) {
    this.t = t
  }
}
