import { exceptions } from '@linqpal/models'
import SmsBuilder from './SmsBuilder'

type Messages =
  | 'lateReminder'
  | 'paymentCollected'
  | 'invoicePaymentProcessingNotification'
  | 'invoicePaymentSuccessNotification'
  | 'invoicePaymentFailNotification'
  | 'invoicePlacedCreatorNotification'
  | 'invoiceDraftNotifications'
  | 'notifyOperations'
  | 'saveInvoiceOpsTeamNotification'
  | 'creditStatusSendBackWithInvoiceId'
  | 'creditStatusSendBackWithoutInvoiceId'
  | 'sendVerification'
  | 'processAchReturnAchPayment'
  | 'loanRepaymentCancelLoanPayment'
  | 'loanRepaymentNotifyCustomer'
  | 'checkBalanceReportAlert'
  | 'rejectLoanOpsTeamNotification'
  | 'heartBeatAlert'
  | 'heartBeatAlertWithMessage'
  | 'heartBeatAlertWithMessageAndPulse'
  | 'invoiceStatusFailNotification'
  | 'invoiceExpiredOneDayNotification'
  | 'invoiceExpiredSevenDaysNotification'
  | 'invoiceFutureLessThreeDaysNotification'
  | 'invoiceTodayNotification'
  | 'invoiceFutureLessEightDaysNotification'
  | 'invoiceFutureSevenDayNotification'
  | 'processInvoiceSuccessMessage'
  | 'processInvoiceSuccessMessageWithErrors'
  | 'processInvoiceErrors'
  | 'sendTabapayReport'

export interface EmailMessageData {
  subject: string
  body: string
}

interface MessageParams {
  dueInDays: number
  url: string
  count: number
  overdueSum: number
  name: string | null | undefined
  supplierName: string
  invoiceNumber: string
  invoiceAmount: number
  accountNumber: string
  invoiceLink: string
  totalAmount: number | string
  invoiceDueDate: string
  receiverName: string
  email: string
  phone: string
  operationAmount: number
  approvedAmount: number
  nextPaymentAmount: number
  availableBalance: number
  heartBeatPulse: string
  heartBeatStep: string
  message: string
  daysLeft: number
  requestAmount: number | string
  date: string
  advancePayment: number
  customerName: string
  record: string
  documentType: string
  capitalizeDocumentType: string
  emailStatus: string
  attachmentsCount: number
  errors: string[]
}

export default class EmailBuilder {
  static getSubjectAndBody(request: {
    key: 'lateReminder'
    data: Pick<MessageParams, 'count' | 'overdueSum' | 'url'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key:
      | 'invoicePlacedCreatorNotification'
      | 'rejectLoanOpsTeamNotification'
      | 'invoiceExpiredOneDayNotification'
    data: Pick<MessageParams, 'name'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'paymentCollected'
    data: Pick<
      MessageParams,
      | 'supplierName'
      | 'invoiceNumber'
      | 'invoiceAmount'
      | 'accountNumber'
      | 'invoiceLink'
    >
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key:
      | 'invoicePaymentProcessingNotification'
      | 'invoicePaymentFailNotification'
    data: Pick<MessageParams, 'name' | 'invoiceNumber' | 'url'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'invoicePaymentSuccessNotification'
    data: Pick<MessageParams, 'name' | 'invoiceNumber'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'invoiceDraftNotifications' | 'loanRepaymentNotifyCustomer'
    data: Pick<MessageParams, 'url'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'notifyOperations'
    data: Pick<
      MessageParams,
      | 'name'
      | 'invoiceNumber'
      | 'totalAmount'
      | 'invoiceDueDate'
      | 'receiverName'
      | 'email'
      | 'phone'
    >
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'saveInvoiceOpsTeamNotification'
    data: Pick<MessageParams, 'name' | 'invoiceNumber' | 'totalAmount'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key:
      | 'creditStatusSendBackWithInvoiceId'
      | 'creditStatusSendBackWithoutInvoiceId'
      | 'sendVerification'
      | 'loanRepaymentCancelLoanPayment'
      | 'invoiceStatusFailNotification'
      | 'invoiceExpiredSevenDaysNotification'
      | 'invoiceTodayNotification'
    data: Pick<MessageParams, 'name' | 'url'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'processAchReturnAchPayment'
    data: Pick<MessageParams, 'invoiceNumber' | 'operationAmount'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'checkBalanceReportAlert'
    data: Pick<
      MessageParams,
      | 'name'
      | 'approvedAmount'
      | 'nextPaymentAmount'
      | 'url'
      | 'availableBalance'
    >
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'heartBeatAlert'
    data: Pick<MessageParams, 'heartBeatPulse' | 'heartBeatStep'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'heartBeatAlertWithMessage'
    data: Pick<MessageParams, 'message'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'heartBeatAlertWithMessageAndPulse'
    data: Pick<MessageParams, 'heartBeatPulse' | 'heartBeatStep' | 'message'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key:
      | 'invoiceFutureLessThreeDaysNotification'
      | 'invoiceFutureLessEightDaysNotification'
    data: Pick<MessageParams, 'url' | 'name' | 'daysLeft'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'invoiceFutureSevenDayNotification'
    data: Pick<MessageParams, 'url' | 'name' | 'invoiceDueDate'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'sendTabapayReport'
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'processInvoiceSuccessMessage'
    data: Pick<
      MessageParams,
      | 'documentType'
      | 'capitalizeDocumentType'
      | 'email'
      | 'invoiceNumber'
      | 'emailStatus'
    >
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'processInvoiceSuccessMessageWithErrors'
    data: Pick<MessageParams, 'capitalizeDocumentType' | 'invoiceNumber'>
  }): EmailMessageData

  static getSubjectAndBody(request: {
    key: 'processInvoiceErrors'
    data: Pick<
      MessageParams,
      'attachmentsCount' | 'email' | 'errors' | 'emailStatus'
    >
  }): EmailMessageData

  static getSubjectAndBody(params: {
    key: Messages
    data?: Partial<MessageParams>
  }): EmailMessageData {
    if (!params.key)
      throw new exceptions.LogicalError(
        'Key should be provided to retrieve message',
      )

    const messages: Record<Messages, () => { subject: string; body: string }> =
      {
        lateReminder: () => ({
          subject: 'Loan payment reminder',
          body: SmsBuilder.getMessage({
            key: 'lateReminder',
            data: {
              count: params.data?.count || 0,
              overdueSum: params.data?.overdueSum || 0,
              url: params.data?.url || '',
            },
          }),
        }),
        paymentCollected: () => ({
          subject: 'Thank you for your invoice payment!',
          body: SmsBuilder.getMessage({
            key: 'paymentCollected',
            data: {
              supplierName: params.data?.supplierName || '',
              invoiceAmount: params.data?.invoiceAmount || 0,
              invoiceNumber: params.data?.invoiceNumber || '',
              invoiceLink: params.data?.invoiceLink || '',
              accountNumber: params.data?.accountNumber || '',
            },
          }),
        }),
        invoicePaymentProcessingNotification: () => ({
          subject: 'Customer Payment Processing',
          body: `${params.data?.name} has made a payment on ${params.data?.invoiceNumber}, the payment is processing. Login to see the status ${params.data?.url}`,
        }),
        invoicePaymentSuccessNotification: () => ({
          subject: 'Customer Invoice Paid',
          body: `${params.data?.name} payment for invoice ${params.data?.invoiceNumber} has been Paid.`,
        }),
        invoicePaymentFailNotification: () => ({
          subject: 'Customer Payment Failed',
          body: `${params.data?.name} payment for invoice  ${params.data?.invoiceNumber} has failed. Please contact customer for other payment options. Login to see the details ${params.data?.url}`,
        }),
        invoicePlacedCreatorNotification: () => ({
          subject: 'Payment Request sent',
          body: `BlueTape: Payment request to ${
            params.data?.name || 'customer'
          } has been sent`,
        }),
        invoiceDraftNotifications: () => ({
          subject: 'Payment Request sent',
          body: SmsBuilder.getMessage({
            key: 'invoiceDraftNotifications',
            data: { url: params.data?.url || '' },
          }),
        }),
        notifyOperations: () => ({
          subject: `${params.data?.name} has sent out an Invoice`,
          body: `<strong>Invoice Number:</strong> ${params.data?.invoiceNumber} <br/>
                 <strong>Invoice Amount:</strong> ${params.data?.totalAmount} <br/>
                 <strong>Invoice Due Date:</strong> ${params.data?.invoiceDueDate} <br/>
                <strong>Payer Name:</strong> ${params.data?.receiverName}<br/>
                <strong>Payer Email:</strong> ${params.data?.email}<br/>
                <strong>Payer Phone:</strong> ${params.data?.phone}<br/>`,
        }),
        saveInvoiceOpsTeamNotification: () => ({
          subject:
            'An invoice with negative amount (refund) was sent out to the customer',
          body: `<div>
              An invoice with negative amount (refund) was sent out to the customer<br>
              Supplier name: <b>${params.data?.name}</b><br>
              Invoice Number: <b>${params.data?.invoiceNumber}</b><br>
              Amount: <b>${params.data?.totalAmount}</b>
              </div>`,
        }),

        creditStatusSendBackWithInvoiceId: () => ({
          subject: 'Credit request notification',
          body: SmsBuilder.getMessage({
            key: 'creditStatusSendBackWithInvoiceId',
            data: {
              name: params.data?.name || '',
              url: params.data?.url || '',
            },
          }),
        }),

        creditStatusSendBackWithoutInvoiceId: () => ({
          subject: 'Credit request notification',
          body: SmsBuilder.getMessage({
            key: 'creditStatusSendBackWithoutInvoiceId',
            data: {
              name: params.data?.name || '',
              url: params.data?.url || '',
            },
          }),
        }),
        sendVerification: () => ({
          subject: `${params.data?.name} Verification`,
          body: `Hello, <br/><br/>
              Welcome to ${params.data?.name}! Before you get started, please verify your email address by clicking on the link below:<br/><br/>
              ${params.data?.url}<br/><br/>
              You’re receiving this message because you have signed up for BlueTape. If you believe you received this email in error, please ignore this message.<br/><br/>
              -Powered by BlueTape<br/><br/><br/>
              1390 Market Street<br/>
              Suite 200<br/>
              San Francisco, CA<br/>
              94102<br/>
              <a href='tel:+14158581185'>************</a>`,
        }),
        processAchReturnAchPayment: () => ({
          subject: 'ACH Pull was returned',
          body: `The ACH Pull for invoice ${params.data?.invoiceNumber} with total amount ${params.data?.operationAmount} is returned`,
        }),
        loanRepaymentCancelLoanPayment: () => ({
          subject: `${params.data?.name}: Auto collecting was turned OFF`,
          body: `<div>Auto collecting was turned OFF for ${params.data?.name}. Click <a href='${params.data?.url}'>here</a> to see the loan.</div>`,
        }),
        loanRepaymentNotifyCustomer: () => ({
          subject: 'Your repayment failed',
          body: SmsBuilder.getMessage({
            key: 'loanRepaymentNotifyCustomer',
            data: { url: params.data?.url || '' },
          }),
        }),
        checkBalanceReportAlert: () => ({
          subject: `Finicity balance report. Environment: ${process.env.LP_MODE}`,
          body: `${params.data?.name} hasn't enough balance for upcoming payment. Has loan amount ${params.data?.approvedAmount}, next payment amount ${params.data?.nextPaymentAmount} and available balance ${params.data?.availableBalance}. <a href='${params.data?.url}'>Visit Loan Application</a>`,
        }),
        rejectLoanOpsTeamNotification: () => ({
          subject: 'A loan application with SSN/EIN duplicate was rejected',
          body: `<div>
            A loan application with SSN/EIN duplicate was rejected<br>
            Company name: <b>${params.data?.name}</b><br>
          </div>`,
        }),
        heartBeatAlert: () => ({
          subject: `Heart beat. Environment: ${process.env.LP_MODE}`,
          body: `Heart beat failed: ${params.data?.heartBeatPulse} - ${params.data?.heartBeatStep}`,
        }),
        heartBeatAlertWithMessage: () => ({
          subject: `Heart beat. Environment: ${process.env.LP_MODE}`,
          body: `Heart beat failed: ${params.data?.message}`,
        }),
        heartBeatAlertWithMessageAndPulse: () => ({
          subject: `Heart beat. Environment: ${process.env.LP_MODE}`,
          body: `Heart beat failed: ${params.data?.message}`,
        }),
        invoiceStatusFailNotification: () => ({
          subject: 'Failed Payment Notification',
          body: SmsBuilder.getMessage({
            key: 'invoiceStatusFailNotification',
            data: { name: params.data?.name, url: params.data?.url || '' },
          }),
        }),
        invoiceExpiredOneDayNotification: () => ({
          subject: 'Payment request notification',
          body: SmsBuilder.getMessage({
            key: 'invoiceExpiredOneDayNotification',
            data: { name: params.data?.name || '' },
          }),
        }),
        invoiceExpiredSevenDaysNotification: () => ({
          subject: 'Payment request notification',
          body: SmsBuilder.getMessage({
            key: 'invoiceExpiredSevenDaysNotification',
            data: {
              name: params.data?.name || '',
              url: params.data?.url || '',
            },
          }),
        }),
        invoiceFutureLessThreeDaysNotification: () => ({
          subject: 'Payment request notification',
          body: SmsBuilder.getMessage({
            key: 'invoiceFutureLessThreeDaysNotification',
            data: {
              name: params.data?.name || '',
              url: params.data?.url || '',
              daysLeft: params.data?.daysLeft || 0,
            },
          }),
        }),
        invoiceTodayNotification: () => ({
          subject: 'Payment request notification',
          body: SmsBuilder.getMessage({
            key: 'invoiceTodayNotification',
            data: {
              name: params.data?.name || '',
              url: params.data?.url || '',
            },
          }),
        }),
        invoiceFutureLessEightDaysNotification: () => ({
          subject: 'Payment request notification',
          body: SmsBuilder.getMessage({
            key: 'invoiceFutureLessEightDaysNotification',
            data: {
              name: params.data?.name || '',
              url: params.data?.url || '',
              daysLeft: params.data?.daysLeft || 0,
            },
          }),
        }),
        invoiceFutureSevenDayNotification: () => ({
          subject: 'Payment request notification',
          body: SmsBuilder.getMessage({
            key: 'invoiceFutureSevenDayNotification',
            data: {
              name: params.data?.name || '',
              url: params.data?.url || '',
              dueDate: params.data?.invoiceDueDate || '',
            },
          }),
        }),
        sendTabapayReport: () => ({
          subject: `Tabapay report`,
          body: `<em style="color: black;">Dear Team,</em><br/>
                 <em style="color: black;">Please find the Tabapay report attached to this email.</em>`,
        }),
        processInvoiceSuccessMessage: () => ({
          subject: `BlueTape Invoice Processing ${params.data?.emailStatus}.`,
          body: `Successfully parsed ${params.data?.documentType} from email: ${params.data?.email}. ${params.data?.capitalizeDocumentType} number ${params.data?.invoiceNumber}.`,
        }),
        processInvoiceSuccessMessageWithErrors: () => ({
          subject: `Action Required: ${params.data?.capitalizeDocumentType} ${params.data?.invoiceNumber} in DRAFT`,
          body: `${params.data?.capitalizeDocumentType} ${params.data?.invoiceNumber} has been placed in Draft status. Some information was not successfully parsed. Please login to www.bluetape.com and review the document, edit and send it to your customer.`,
        }),
        processInvoiceErrors: () => ({
          subject: `BlueTape Invoice Processing ${params.data?.emailStatus}.`,
          body: `Unable to parse invoices from the email: ${
            params.data?.email
          }. Invoices count ${params.data?.attachmentsCount}.
          ${
            params.data?.errors && params.data?.errors.length === 1
              ? '<p>Reason: ' + params.data?.errors[0] + '</p>'
              : '<p>Reason:</p>' +
                (params.data?.errors || [])
                  .map((error) => `<p>${error}</p>`)
                  .join('')
          }
          `,
        }),
      }

    const handler = messages[params.key]

    return handler()
  }
}
