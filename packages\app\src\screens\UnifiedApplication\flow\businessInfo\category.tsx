import React, { FC } from 'react'
import { BtSelect } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { MAIN_ID } from '@linqpal/models/src/dictionaries/onboarding'

const Category: FC = observer(() => {
  const { t } = useTranslation('application')
  const { t: ot } = useTranslation('onboarding')
  const store = useUnifiedApplication()

  const handleChange = (selected: any) => {
    console.log('selected', selected)
    store.document.data.businessInfo.category = selected.value
  }

  const options = [
    { value: MAIN_ID.SUB_CONTRACTOR, label: ot('subContractor') },
    { value: MAIN_ID.GENERAL_CONTRACTOR, label: ot('generalContractor') },
    {
      value: MAIN_ID.DEALER_RETAILER_SUPPLIER,
      label: ot('dealerRetailerSupplier'),
    },
    {
      value: MAIN_ID.MANUFACTURER_DISTRIBUTOR,
      label: ot('manufacturerDistributor'),
    },
    {
      value: MAIN_ID.ARCHITECT_INTERIOR_DESIGNER,
      label: ot('architectInteriorDesigner'),
    },
    { value: MAIN_ID.ENGINEER_CONSULTANT, label: ot('engineerConsultant') },
    {
      value: MAIN_ID.DEVELOPER_PROPERTY_OWNER,
      label: ot('developerPropertyOwner'),
    },
    { value: MAIN_ID.OTHER, label: ot('other') },
  ]

  return (
    <BtSelect
      value={store.document.data.businessInfo.category || ''}
      onChange={handleChange}
      label={t('Business.CategoryLabel')}
      placeholder={t('Business.CategoryPlaceholder')}
      options={options}
      keyName="value"
      labelName="label"
      testID="UnifiedApplication.BusinessInfo.Category"
    />
  )
})

export default {
  component: Category,
  title: {
    text: 'Business.Category',
  },
  showFooterMessage: true,
}
