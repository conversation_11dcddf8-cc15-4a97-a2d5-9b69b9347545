import React from 'react'
import { observer } from 'mobx-react-lite'
import Sidebar from '../../../../../ui/atoms/Sidebar'
import { Spacer } from '../../../../../ui/atoms'
import { phoneMask } from '../../../../../utils/helpers/masking'
import { useTabInvoice } from '../../../../../store/ScreensStore/Supplier/supplierInvoiceTab'
import { SidebarDetailItem } from '../../../../../ui/molecules'
import { useTranslation } from 'react-i18next'
import { AttachmentLine } from '../../Sidebar/ViewSidebarComponents/AttachmentLine'
import { DateLine } from '../../Sidebar/ViewSidebarComponents/DateLine'
import { SubtotalLine } from '../../Sidebar/ViewSidebarComponents/SubtotalLine'
import { AmountLine } from '../../Sidebar/ViewSidebarComponents/AmountLine'
import { AddressLine } from '../../Sidebar/ViewSidebarComponents/AddressLine'
import { QuoteViewSidebarFooter } from './QuoteViewSidebarFooter'
import { EInvoiceType } from '@linqpal/models'
import moment from 'moment'
import { ReceivableStatusLabel } from '../../../../../ui/molecules/StatusLabel/ReceivableStatusLabel'

interface IQuoteViewSidebarProps {
  onClose?: () => void
}

export const QuoteViewSidebar: React.FC<IQuoteViewSidebarProps> = observer(
  (props) => {
    const {
      draftModel: quote,
      customer,
      isView,
      cancelViewSideBar,
      typeOfInvoice,
    } = useTabInvoice()

    const { t } = useTranslation('global')

    return (
      <Sidebar
        visible={isView && quote.type === EInvoiceType.QUOTE}
        width={600}
        title={`${typeOfInvoice} Details`}
        onClose={cancelViewSideBar}
        footer={<QuoteViewSidebarFooter />}
        footerStyle={{
          height: quote.getImportSource.isImported ? 128 : 100,
          flexDirection: 'none',
        }}
        subTitle={customer.name}
        {...props}
      >
        <Spacer height={32} />

        <SidebarDetailItem
          label={t('Receivables.quote.number')}
          content={quote.invoice_number}
        />
        <SidebarDetailItem
          label={t('Receivables.quote.status')}
          content={<ReceivableStatusLabel receivable={quote} />}
        />
        <DateLine receivable={quote} />
        <SubtotalLine receivable={quote} />
        <AmountLine receivable={quote} />
        <SidebarDetailItem
          label={t('Receivables.authorization-deadline')}
          content={
            quote.quoteDetails?.authorization_deadline
              ? moment(quote.quoteDetails?.authorization_deadline)
                  .tz('America/Chicago')
                  .format('MM/DD/YYYY')
              : ''
          }
        />
        <AddressLine receivable={quote} />
        <AttachmentLine receivable={quote} />

        <Spacer height={32} />

        <SidebarDetailItem
          label={t('InvoiceViewSidebar.business-name-label')}
          content={customer.name}
        />
        <SidebarDetailItem
          label={t('InvoiceViewSidebar.customer-number-label')}
          content={quote.customer_account_id || 'N/A'}
        />
        <SidebarDetailItem
          label={t('InvoiceViewSidebar.contact-name-label')}
          content={customer.contact}
        />
        <SidebarDetailItem
          label={t('InvoiceViewSidebar.phone-label')}
          content={phoneMask(customer.phone)}
        />
      </Sidebar>
    )
  },
)
