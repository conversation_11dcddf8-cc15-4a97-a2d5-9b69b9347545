import { Flow } from './FlowController_v1'
import { Steps as _ } from './ApplicationSteps'
import { IUnifiedApplicationOptions } from '@linqpal/models/src/types/unifiedApplication/IUnifiedApplicationOptions'
import { IUnifiedApplicationDraft } from '@linqpal/models/src/types/unifiedApplication/IUnifiedApplicationDraft'

export class UnifiedApplicationFlow {
  flow: Flow<IUnifiedApplicationDraft, IUnifiedApplicationOptions> = {
    [_.businessInfo._]: {
      firstStepChoice: (doc) => doc?.initialStep || _.businessInfo.email.path,
      [_.businessInfo.email._]: '',
      [_.businessInfo.category._]: '',
      categoryChoice: () => _.businessInfo.businessName.path, // will be replaced with actual logic
      [_.businessInfo.trade._]: '',
      [_.businessInfo.businessName._]: '',
      [_.businessInfo.businessPhone._]: '',
      [_.businessInfo.businessAddress._]: '',
      inHouseCreditChoice: () => _.businessInfo.startDate.path, // will be replaced with actual logic
      [_.businessInfo.startDate._]: '',
      [_.businessInfo.type._]: '',
      [_.businessInfo.ein._]: '',
    },

    [_.finance._]: {
      [_.finance.revenue._]: '',
      revenueChoice: () => _.finance.debt.path, // will be replaced with actual logic
      [_.finance.debt._]: '',
      debtChoice: () => _.finance.howMuchCredit.path, // will be replaced with actual logic
      [_.finance.howMuchCredit._]: '',
      goto: () => _.businessOwner.isOwner.path,
      [_.finance.arAdvanceRequestedLimit._]: '',
    },

    [_.businessOwner._]: {
      alreadySubmittedChoice: () => _.businessOwner.isOwner.path, // will be replaced with actual logic
      [_.businessOwner.isOwner._]: { skipPath: _.bank.details._ },
      isOwnerChoice: () => _.businessOwner.ownershipPercentage.path, // will be replaced with actual logic
      [_.businessOwner.ownershipPercentage._]: '',
      goto: () => _.businessOwner.address.path,
      [_.businessOwner.isAuthorized._]: { skipPath: _.bank.details._ },
      isAuthorizedChoice: () => _.businessOwner.address.path, // will be replaced with actual logic
      [_.businessOwner.authorizedDetails._]: '',
      [_.businessOwner.address._]: '',
      [_.businessOwner.birthdate._]: '',
      [_.businessOwner.ssn._]: '',
    },

    [_.coOwnerInfo._]: {
      coOwnersChoice: () => _.bank.details.path, // will be replaced with actual logic
      [_.coOwnerInfo.coOwners._]: '',
    },

    [_.bank._]: {
      [_.bank.details._]: '',
    },

    [_.review._]: {
      [_.review.review._]: '',
      [_.review.preview._]: '',
      [_.review.agreement._]: '',
    },
  }
}
