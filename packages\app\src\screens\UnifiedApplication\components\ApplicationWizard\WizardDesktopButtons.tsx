import { observer } from 'mobx-react'
import ApplicationStore from '../../../GeneralApplication/Application/ApplicationStore'
import { StyleSheet, View } from 'react-native'
import { BtButton, BtText } from '@linqpal/components/src/ui'
import React, { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { Steps } from '../../../GeneralApplication/Store/ApplicationSteps'
import RootStore from '../../../../store/RootStore'

interface IProps {
  canSkip?: boolean
}

export const WizardDesktopButtons: FC<IProps> = observer(
  ({ canSkip = true }) => {
    const store = useUnifiedApplication()

    const cameFromReview = store.previousStep.includes(Steps.review._)
    const canMoveNext = store.currentGroup !== Steps.review._

    // prettier-ignore
    return (
      <View style={styles.wrapper}>
        {canSkip
          ? cameFromReview
            ? <GoToReviewButton />
            : <SkipButton />
          : null
        }
        {canMoveNext
          ? <NextButton />
          : null
        }
      </View>
    )
  },
)

const SkipButton = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  // TODO: VK: Unified: review
  const inviteLabel =
    store.currentStep === Steps.businessOwner.authorizedDetails._
      ? ApplicationStore.inviteButton(true)
      : undefined

  return (
    <BtButton
      onPress={() => store.skipStep()}
      appearance={'ghost'}
      disabled={RootStore.isBusy}
      testID="UnifiedAppication.Wizard.SkipButton"
    >
      {() => (
        <BtText style={styles.buttonLabel}>
          {inviteLabel ? t('Next') : t('SkipForNow')}
        </BtText>
      )}
    </BtButton>
  )
})

const NextButton = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  // TODO: VK: Unified: review
  const inviteLabel =
    store.currentStep === Steps.businessOwner.authorizedDetails._
      ? ApplicationStore.inviteButton(true)
      : undefined

  return (
    <BtButton
      onPress={() => store.moveForward()}
      style={{ width: inviteLabel ? 238 : 150 }}
      disabled={RootStore.isBusy || !store.isCurrentStepValid}
      testID="UnifiedApplication.Wizard.NextButton"
    >
      {inviteLabel || t('Next')}
    </BtButton>
  )
})

const GoToReviewButton = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  return (
    <BtButton
      onPress={() => store.goToStep(store.previousStep)}
      appearance={'ghost'}
      disabled={RootStore.isBusy}
      testID="UnifiedApplication.Wizard.GoToReviewButton"
    >
      {() => <BtText style={styles.buttonLabel}>{t('GoToReviewPage')}</BtText>}
    </BtButton>
  )
})

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'flex-end',
    marginTop: 20,
    gap: 20,
  },
  buttonLabel: {
    fontWeight: '700',
    fontSize: 16,
    color: '#668598',
  },
})
