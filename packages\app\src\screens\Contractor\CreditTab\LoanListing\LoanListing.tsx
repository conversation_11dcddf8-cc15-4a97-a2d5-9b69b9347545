import React, { useCallback, useMemo, useState } from 'react'
import { View } from 'react-native'
import Wrapper from '../../Wrapper'
import { paths } from '../../../links'
import CreditStore from '../CreditStore'
import { ReadytoPayWithVirtualCard } from '../PayNowWithVirtualCard'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react'
import { toJS } from 'mobx'
import styles from '../styles'
import { ToggleItem } from '../ViewLoan/ToggleItem'
import { LoanItem } from '../LoanListing/LoanItem'
import { ActiveLoansTitle } from '../LoanListing/ActiveLoansTitle'
import { PenaltyInterest } from '../LoanSchedule/PenaltyInterest'
import { LineOfCreditOverview } from './LineOfCreditOverview'
import { ListTitle } from './ListTitle'
import { TradeCreditInfoCards } from './TradeCreditINforCards'
import { TopBar } from '../../../TradeCredit/Components/StackContainer'
import { Spacer } from '../../../../ui/atoms'

export const LoanListing = observer(({ navigation }) => {
  const { t } = useTranslation('global')
  const {
    hasUnusedVirtualCard,
    approvedLoans,
    isLoanIdExists,
    previousLoans,
    activeLoans,
    setLoanApplication,
    setLoan,
    penaltySum,
  } = CreditStore
  const loans = toJS(approvedLoans)
  const [toggle, setToggle] = useState(
    previousLoans.length && !activeLoans.length ? 0 : 1,
  )

  const onLoanPress = useCallback(
    (loan) => {
      if (isLoanIdExists(loan)) {
        setLoanApplication(loan)
        setLoan(null)
        navigation.navigate(paths.Console.Credit.LoanDetails, {
          id: loan.lms_id,
          supplierName: CreditStore.supplier,
        })
      }
    },
    [navigation, isLoanIdExists, setLoanApplication, setLoan],
  )

  const ls = useMemo(() => {
    if (!loans || loans.length === 0) return null
    return (
      <>
        {!!activeLoans.length && !!previousLoans.length ? (
          <View
            style={{
              backgroundColor: '#F4F9FD',
              height: 40,
              borderRadius: 10,
              flexDirection: 'row',
              padding: 3,
              marginBottom: 20,
            }}
          >
            <ToggleItem
              active={toggle === 1}
              onPress={() => setToggle(1)}
              title={<ActiveLoansTitle />}
              activeColor="#003353"
            />
            <ToggleItem
              active={toggle === 0}
              onPress={() => setToggle(0)}
              title={t('LoanListing.previous-draws')}
              activeColor="#003353"
            />
          </View>
        ) : activeLoans.length ? (
          <ListTitle title={t('LoanListing.active-draws').toUpperCase()} />
        ) : previousLoans.length ? (
          <ListTitle title={t('LoanListing.previous-draws').toUpperCase()} />
        ) : null}
        {toggle
          ? activeLoans.map((l) => (
              <LoanItem
                key={l._id}
                loan={l}
                onPress={(loanItem) => onLoanPress(loanItem)}
              />
            ))
          : previousLoans.map((l) => (
              <LoanItem
                closed
                key={l._id}
                loan={l}
                onPress={(loanItem) => onLoanPress(loanItem)}
              />
            ))}
      </>
    )
  }, [loans, t, onLoanPress, activeLoans, previousLoans, toggle])

  return (
    <>
      <TopBar
        label={t('LoanListing.header')}
        getBreadCrumbs={() => [
          { label: t('Menu.TradeCredit') },
          { label: t('Menu.AccountOverview') },
        ]}
      />

      <TradeCreditInfoCards />
      <Spacer height={20} />

      <Wrapper>
        <LineOfCreditOverview />

        {(!!penaltySum || hasUnusedVirtualCard || ls) && (
          <View style={styles.separator} />
        )}

        {!!penaltySum && <PenaltyInterest aggregate />}
        {hasUnusedVirtualCard && <ReadytoPayWithVirtualCard mobileView />}
        {ls}
      </Wrapper>
    </>
  )
})
