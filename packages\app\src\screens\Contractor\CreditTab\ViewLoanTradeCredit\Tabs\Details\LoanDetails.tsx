import React, { useCallback, useMemo } from 'react'
import { commonColors } from '@linqpal/common-frontend/src/theme'
import { useTranslation } from 'react-i18next'
import numbro from 'numbro'
import CreditStore from '../../../CreditStore'
import { Spacer } from '@linqpal/components/src/ui'
import { paths } from '../../../../../links'
import { DetailDrawStatusItem, DetailListItem } from './DetailListItem'
import { observer } from 'mobx-react'
import {
  AccountStatus,
  AccountStatusOptions,
} from '@linqpal/models/src/dictionaries/tradeCredit'
import { Invoices } from './Invoices'
import FileLink from './DrawDisclosureLink'
import { routes } from '@linqpal/models'
import { fileDownloader } from '@linqpal/common-frontend/src/helpers'
import { toCurrency, toPercentage } from '@linqpal/models/src/helpers/formatter'
import { formatDate } from '@linqpal/models/src/helpers/date'

const drawStatusStyles = {
  [AccountStatusOptions.PAST_DUE]: '#EC002A',
  [AccountStatusOptions.GOOD_STANDING]: '#19262F',
}

export const LoanDetails = observer(
  ({
    loan,
    navigation,
    loanStatus,
    option,
    bankAccount,
    isDrawOpen,
    supplierName,
    isMobile,
  }) => {
    const { t } = useTranslation('tradeCredit')
    const {
      supplierRepays,
      creditAppliedInvoices: invoices,
      loanApplication,
    } = CreditStore
    const paidByVC = useMemo((): boolean => !!loan?.cardId, [loan?.cardId]) // if no cardId it's not VC loan
    const totalAmount = invoices
      ? invoices.reduce((sum, i) => sum + numbro(i.total_amount).value(), 0)
      : 0

    const onViewInvoice = (invoice) => {
      navigation.navigate(paths.Console.Payables._self, {
        screen: paths.Console.Payables.Receipt,
        params: { id: invoice._id },
      })
    }
    const onViewAgreement = useCallback(() => {
      routes.tradeCredit
        .viewDrawAgreement({ id: loanApplication._id })
        .then((r) => fileDownloader(r.url, {}, r.fileName))
    }, [loanApplication])

    if (!loan) return null

    return (
      <>
        <DetailListItem
          label={t('tradeCredit.drawDetails.details.installments')}
          value={loan.loanTemplate?.installmentsNumber}
        />
        <DetailListItem
          label={t('tradeCredit.drawDetails.details.drawApprovalDate')}
          value={formatDate(loanApplication?.decisionDate)}
        />
        <DetailListItem
          label={t('tradeCredit.drawDetails.details.invoiceAmount')}
          value={toCurrency(totalAmount)}
        />

        <DetailListItem
          label={t('tradeCredit.drawDetails.details.fee', {
            percentage: toPercentage(
              loan?.loanParameters?.[0]?.loanFeePercentage,
              'fraction',
            ),
          })}
          value={toCurrency(loan?.fee)}
        />

        <DetailListItem
          label={t('tradeCredit.drawDetails.details.drawBalance')}
          value={toCurrency(loan?.fee + loan?.amount)}
        />

        <DetailListItem
          label={t('tradeCredit.drawDetails.details.drawId')}
          value={isMobile ? loan?.id.substr(0, 25) + '...' : loan?.id}
        />

        <DetailDrawStatusItem
          label={t('tradeCredit.drawDetails.details.drawStatus')}
          value={AccountStatus[loanStatus]}
          additionalValue={isDrawOpen && 'Open / '}
          valueColor={drawStatusStyles[loanStatus]}
          isMobile={isMobile}
        />

        {loan?.loanParameters?.[0]?.downPaymentAmount && (
          <DetailListItem
            label={t('tradeCredit.drawDetails.details.downPayment', {
              percentage: toPercentage(
                loan.loanParameters[0].downPaymentPercentage,
              ),
            })}
            value={toCurrency(loan.loanParameters[0].downPaymentAmount)}
          />
        )}

        {!supplierRepays && (
          <DetailListItem
            label={t('tradeCredit.drawDetails.details.fundingAccount')}
            value={bankAccount}
            valueColor={commonColors.accentText}
          />
        )}
        {paidByVC && (
          <DetailListItem
            label={t('tradeCredit.drawDetails.details.virtualCardDetails')}
            value={t('tradeCredit.drawDetails.details.viewCardNumber')}
            valueColor={commonColors.statusActiveCredit}
          />
        )}
        <DetailListItem
          label={t('tradeCredit.drawDetails.details.drawDisclosure')}
          value={
            <FileLink
              filename={
                t('tradeCredit.drawDetails.details.drawDisclosureFilename') +
                '.pdf'
              }
              onPress={onViewAgreement}
            />
          }
        />
        <Spacer />

        <Invoices
          invoices={invoices as any}
          isMobile={isMobile}
          navigation={navigation}
          supplierName={supplierName}
          onSelect={onViewInvoice}
        />
      </>
    )
  },
)
