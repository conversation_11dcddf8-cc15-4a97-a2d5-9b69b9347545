import {
  Authentication,
  cbw,
  Company,
  Draft,
  Invoice,
  LMS,
  LoanApplication,
  LoanPaymentPlan,
  Operation,
} from '@linqpal/common-backend'
import { IInvoice } from '@linqpal/common-backend/src/models/types'
import { dictionaries, exceptions } from '@linqpal/models'
import chai from 'chai'
import chaiHttp from 'chai-http'
import moment from 'moment-timezone'
import nock from 'nock'
import sinon from 'sinon'
import {
  beforeEachMockEncryption,
  beforeEachMockSecretsManager,
} from './helper'
import lambda from 'lambda-tester'
import {
  loanAppChangePaymentPlan,
  loanAppLoanDisburse,
  loanAppLoanDisburseDone,
} from '../linqpal'
import { IAgreementFileDetails } from '@linqpal/common-backend/src/services/agreement/types'
import * as AgreementService from '@linqpal/common-backend/src/services/agreement/agreement.service'
import { LOAN_PLAN_TYPE } from '../../models/src/dictionaries'

chai.use(chaiHttp)
chai.should()

const country_group_usbased = {
  id: '6231fe0b446025b18bf01c90',
  groupName: 'USBASED',
  description: 'US BASED',
  countryCodes: ['US'],
  active: true,
}

const mcc_group_suppliers = {
  id: '6231fe4d446025b18bf01ced',
  groupName: 'SUPPLIERS',
  description: 'SUPPLIERS ONLY',
  mccCodes: ['*'],
  active: true,
}

const card_1 = {
  cardId: '80c242e8-be55-4024-b175-5b824c335b2c',
  cardType: 'VIRTUAL',
  cardStatus: 'ACTIVATED',
  cardMaskNumber: '5433*******6636',
  cardExpiryDate: '202204',
  mccGroupId: mcc_group_suppliers.id,
  countryGroupId: country_group_usbased.id,
  limits: [],
}

const card_1_name = 'Contractor 1'
const card_1_limit = 101

describe('Change Loan App Payment Plan', () => {
  beforeEachMockEncryption()
  beforeEachMockSecretsManager()

  let requesterStub: sinon.SinonStub
  let createLoanStub: sinon.SinonStub
  let activateLoanStub: sinon.SinonStub
  let syncLoanApplicationFieldsStub: sinon.SinonStub
  let verifyRoleStub: sinon.SinonStub
  let mockAgreementSync: sinon.SinonStub

  async function mockCbwGeneral(_url: string, data: any) {
    let resp: any
    switch (data.payload.transactionType) {
      case 'UPDATE_STATUS':
        resp = { card: card_1 }
        break
      case 'GET_CARD':
        resp = { card: card_1 }
        break
      case 'GL_TRANSFER':
        resp = {
          transactionNumber: 'gl-123',
          transactionStatus: dictionaries.CbwTransactionStatus.Pending,
        }
        break
      case 'ACH_OUT':
        resp = { transactionNumber: 'gl-123' }
        break
      default:
        throw new exceptions.LogicalError(
          `Invalid request ${data.payload.transactionType}`,
        )
    }
    return Promise.resolve(resp)
  }

  async function mockSuccessGL() {
    return Promise.resolve({
      transactionStatus: dictionaries.CbwTransactionStatus.Processed,
    })
  }

  beforeEach(async () => {
    process.env.LP_MODE = 'test'
    process.env.LP_CBW_ACH_IDENTIFICATION = '123'
    process.env.LP_CBW_GL_IDENTIFICATION = 'GL123'
    process.env.LP_CBW_FBO_IDENTIFICATION = 'FBO123'
    process.env.LP_CBW_ACH_CUSTOMER_ID = '123'

    const requester = cbw.initCbwApiRequester()
    requesterStub = sinon.stub(requester, 'post').callsFake(mockCbwGeneral)
    createLoanStub = sinon.stub(LMS, 'createLoan').callsFake(async () => ({
      id: 'lms-123',
      paymentDate: moment().toISOString(),
      amount: 1000,
      fee: 100,
    }))
    activateLoanStub = sinon.stub(LMS, 'activateLoan').callsFake(async () => {})
    syncLoanApplicationFieldsStub = sinon
      .stub(LMS, 'syncLoanApplicationFields')
      .callsFake(async () => {})
    verifyRoleStub = sinon
      .stub(Authentication, 'verifyRole')
      .callsFake(async () => true)
    const agreementMockData: IAgreementFileDetails = {
      url: 'url',
      fileName: 'fileName',
    }
    mockAgreementSync = sinon
      .stub(AgreementService, 'createAgreementForLoanApplication')
      .callsFake(() => Promise.resolve(agreementMockData))
  })

  afterEach(async () => {
    verifyRoleStub.restore()
    createLoanStub.restore()
    activateLoanStub.restore()
    syncLoanApplicationFieldsStub.restore()
    requesterStub.restore()
    nock.cleanAll()
    mockAgreementSync.restore()
  })

  async function mockData() {
    const company = await Company.create({})
    const invoice: IInvoice = await Invoice.create({
      total_amount: card_1_limit - 0.5,
      status: dictionaries.invoiceSchemaStatus.draft,
      payer_id: company.id,
    })

    const [paymentPlan, plan] = await LoanPaymentPlan.create([
      {
        days: 90,
        fee: 4,
        firstPaymentDelayDays: 34,
        frequency: 'weekly',
        lmsTemplateId: '7ce0034d-800e-4ce0-9585-e9dd17338878',
        name: '90vc',
        term: 9,
        type: LOAN_PLAN_TYPE.VIRTUAL_CARD,
      },
      {
        days: 90,
        fee: 6,
        firstPaymentDelayDays: 30,
        frequency: 'weekly',
        lmsTemplateId: '4a20fc85-e1e9-4945-a864-5ccf23e9f93a',
        name: 'Direct Term - No VC',
        term: 9,
        type: LOAN_PLAN_TYPE.NO_SUPPLIER,
      },
    ])

    const app = await LoanApplication.create({
      invoiceDetails: {
        invoiceId: invoice.id,
        paymentPlan: paymentPlan.id,
        cardId: card_1.cardId,
      },
      metadata: { paymentPlan: paymentPlan.toJSON() },
      company_id: company.id,
      status: dictionaries.LOAN_APPLICATION_STATUS.APPROVED,
      approvedAmount: card_1_limit,
    })

    await Draft.create({
      company_id: company.id,
      type: 'general_application',
      data: {
        businessInfo: {
          group: 'businessInfo',
          items: [
            { identifier: 'businessName', content: card_1_name },
            {
              identifier: 'ein',
              content: {
                hash: 'ein',
              },
            },
          ],
        },
      },
    })

    return { app, plan, invoice }
  }

  it('should change payment plan on gl transfer processed', async () => {
    const { app, plan, invoice } = await mockData()
    let resp = await lambda(loanAppChangePaymentPlan)
      .event({
        app_id: app._id.toString(),
        data: {
          supplierName: 'Supplier',
          routingNumber: '*********',
          accountNumber: '*********',
          paymentPlan: plan,
        },
      })
      .expectResult()
    resp.app_id.should.eq(app.id.toString())
    resp.data.status.should.eq('pending')
    resp.data.prev_metadata.should.exist
    resp.data.prevInvoiceDetails.should.exist
    resp.data.invoices_id.should.exist
    let loanApp = await LoanApplication.findById(resp.app_id)
    JSON.stringify(loanApp!.metadata!).should.not.eq(
      JSON.stringify(app.metadata),
    )

    resp = await lambda(loanAppLoanDisburse).event(resp).expectResult()
    resp.app_id.should.eq(app.id.toString())
    resp.data.status.should.eq('success')
    resp.data.prev_metadata.should.exist
    resp.data.prevInvoiceDetails.should.exist
    resp.data.invoices_id.should.exist

    let opDisburse = await Operation.findOne({
      owner_id: app.id,
      type: dictionaries.OPERATION_TYPES.LOAN.ISSUE,
    })
    opDisburse!.status.should.eq(dictionaries.OPERATION_STATUS.SUCCESS)

    requesterStub.callsFake(mockSuccessGL)
    resp = await lambda(loanAppLoanDisburse).event(resp).expectResult()
    resp.app_id.should.eq(app.id.toString())
    resp.data.status.should.eq('success')
    opDisburse = await Operation.findOne({
      owner_id: app.id,
      type: dictionaries.OPERATION_TYPES.LOAN.ISSUE,
    })
    opDisburse!.status.should.eq(dictionaries.OPERATION_STATUS.SUCCESS)
    loanApp = await LoanApplication.findById(app.id)
    JSON.stringify(loanApp!.metadata!).should.not.eq(
      JSON.stringify(app.metadata!),
    )
    loanApp!.approvedAmount.should.eq(invoice.total_amount)
    await lambda(loanAppLoanDisburseDone).event(resp).expectResult()
    const op = await Operation.findOne({
      owner_id: invoice.id,
      type: dictionaries.OPERATION_TYPES.INVOICE.PAYMENT,
    })
    op!.metadata.payment_method!.should.eq(dictionaries.PAYMENT_METHODS.LOAN)
    loanApp!.approvedAmount.should.eq(op!.amount)
    createLoanStub.calledOnce.should.eq(true)
    activateLoanStub.calledOnce.should.eq(true)
    syncLoanApplicationFieldsStub.calledOnce.should.eq(true)
  })
})
