import { observer } from 'mobx-react'
import { colors } from '@linqpal/components/src/theme'
import { StyleSheet, TouchableOpacity } from 'react-native'
import ApplicationStore from '../../../GeneralApplication/Application/ApplicationStore'
import { BlackArrowBack, IconArrowBack } from '../../../../assets/icons'
import { BtText } from '@linqpal/components/src/ui'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useResponsive } from '@linqpal/components/src/hooks'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { Steps } from '../../../GeneralApplication/Store/ApplicationSteps'

interface IProps {
  onBack: () => void
}

export const WizardBackButton = observer(({ onBack }: IProps) => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const color = sm ? colors.accentText : colors.primary
  // TODO: VK: review padding for preview in combined review, maybe hide common back for review
  const isPreview = store.currentStep === Steps.review.preview.path

  return (
    <div
      style={{
        position: 'sticky',
        top: 0,
        padding: isPreview ? '20px 0' : 0,
        zIndex: 1000,
        backgroundColor: 'white',
      }}
    >
      <TouchableOpacity
        style={styles.wrapper}
        onPress={onBack}
        disabled={ApplicationStore.buttonLoading}
        testID="UnifiedApplication.Wizard.BackButton"
      >
        {sm ? (
          <IconArrowBack stroke={color} />
        ) : (
          <BlackArrowBack stroke={color} style={{ height: 20 }} />
        )}

        <BtText style={styles.label}>{t('Back')}</BtText>
      </TouchableOpacity>
    </div>
  )
})

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontWeight: '500',
    color: colors.accentText,
    marginTop: -2,
    marginLeft: 15,
  },
})
