import React from 'react'
import { useTranslation, Trans } from 'react-i18next'
import { StyleSheet, Text, View } from 'react-native'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { commonColors } from '@linqpal/common-frontend/src/theme'
import { EInvoiceAddressType, EInvoiceType } from '@linqpal/models'
import { Invoice } from './InvoiceDetails'
import capitalize from 'lodash/capitalize'
import { Divider } from 'react-native-paper'
import { currencyMask, dateMask } from '../../../../utils/helpers/masking'
import { TooltipIconModal } from '../../../../ui/atoms/TooltipIconModal'

export const OtherInvoiceInfo: React.FC<{
  invoice: Invoice
}> = ({ invoice }) => {
  const { t } = useTranslation('global')

  const hasQuote = !!(invoice.quoteId && invoice.quote_number) // double check for quote id to ensure invoice is linked to quote

  const dueDate = dateMask(invoice.invoice_due_date)
  const dueLabel =
    invoice.type === EInvoiceType.INVOICE
      ? t('Receivables.due-on', { date: dueDate })
      : ''

  return (
    <>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
        <Text style={composeStyle(styles.normalText, { maxWidth: '50%' })}>
          {`${capitalize(invoice?.type).replace('_', ' ')} #${
            invoice.invoice_number
          }`}
        </Text>
        <Text style={styles.valueText}>{dueLabel}</Text>
      </View>
      {!!invoice.lateFee && (
        <>
          <Divider style={styles.divider} />
          <AmountDisplay
            amount={invoice.total_amount}
            label={t('TabInvoice.InvoiceAmount')}
          />

          <Divider style={styles.divider} />
          <AmountDisplay
            amount={invoice.lateFee}
            label={t('TabInvoice.LateInterestCharge')}
          />
        </>
      )}

      {hasQuote && (
        <View>
          <Divider style={styles.divider} />
          <Text style={styles.normalText}>
            {t('InvoiceDetails.quote-number', {
              quoteNumber: invoice.quote_number,
            })}
          </Text>
        </View>
      )}

      <Divider style={styles.divider} />
      {!invoice?.supplierInvitationDetails && <AddressType invoice={invoice} />}
    </>
  )
}

const AddressType: React.FC<{ invoice: Invoice }> = ({ invoice }) => {
  const { t } = useTranslation('global')
  if (invoice.addressType === EInvoiceAddressType.DELIVERY) {
    return (
      <>
        <View style={styles.addressTypeContainer}>
          <View style={styles.deliverToContainer}>
            <Text style={styles.normalText}>
              {t('InvoiceDetails.deliver-to')}
            </Text>
            {invoice?.invoice_document && (
              <TooltipIconModal
                tooltip={
                  <Trans
                    t={t}
                    i18nKey="InvoiceDetails.deliver-tooltip"
                    components={{
                      bold: <Text style={{ fontWeight: '700' }} />,
                    }}
                  />
                }
                // eslint-disable-next-line i18next/no-literal-string
                iconName="help-circle-outline"
                containerStyle={styles.tooltipContainer}
                size={19}
                tooltipWidth={275}
              />
            )}
          </View>
          <Text
            style={composeStyle(styles.normalText, {
              maxWidth: '50%',
              color: commonColors.textDark,
              textAlign: 'right',
            })}
          >
            {invoice.address}
          </Text>
        </View>
        {invoice.attention && (
          <>
            <Divider style={styles.divider} />
            <View style={styles.addressTypeContainer}>
              <Text style={styles.normalText}>
                {t('InvoiceDetails.attention-to')}
              </Text>
              <Text
                style={composeStyle(styles.normalText, {
                  maxWidth: '50%',
                  color: commonColors.textDark,
                  textAlign: 'right',
                })}
              >
                {invoice.attention}
              </Text>
            </View>
          </>
        )}
      </>
    )
  } else if (invoice.addressType === EInvoiceAddressType.PICKUP) {
    return <Text style={styles.normalText}>{t('InvoiceDetails.pick-up')}</Text>
  } else if (invoice.addressType) {
    return <Text style={styles.normalText}>{invoice.addressType}</Text>
  } else {
    return null
  }
}

const AmountDisplay = ({ amount, label }) => (
  <View style={styles.addressTypeContainer}>
    <Text style={composeStyle(styles.normalText, { maxWidth: '50%' })}>
      {label}
    </Text>
    <Text style={styles.valueText}>{currencyMask(amount)}</Text>
  </View>
)

const styles = StyleSheet.create({
  boldText: {
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 28,
    color: commonColors.textDark,
  },
  normalText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 21,
    color: '#99ADBA',
  },
  valueText: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '500',
    maxWidth: '50%',
    textAlign: 'right',
    color: commonColors.textDark,
  },
  addressTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  deliverToContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  tooltipContainer: {
    alignItems: 'center',
    marginTop: 3,
  },
  divider: {
    marginTop: 14,
    marginBottom: 14,
  },
  labelStyle: {
    color: '#99ADBA',
    marginTop: 7,
  },
})
