import { FC } from 'react'
import { Steps } from '../../GeneralApplication/Store/ApplicationSteps'

import details from '../../GeneralApplication/Application/flow/bank/BankDetailsStep'
import businessAddress from '../../GeneralApplication/Application/flow/businessInfo/businessAddress'
import businessPhone from '../../GeneralApplication/Application/flow/businessInfo/businessPhone'
import ein from '../../GeneralApplication/Application/flow/businessInfo/ein'
import email from '../../UnifiedApplication/flow/businessInfo/email'
import startDate from '../../GeneralApplication/Application/flow/businessInfo/startDate'
import trade from '../../GeneralApplication/Application/flow/businessInfo/trade'
import type from '../../GeneralApplication/Application/flow/businessInfo/type'
import address from '../../GeneralApplication/Application/flow/businessOwner/address'
import ownershipPercentage from '../../GeneralApplication/Application/flow/businessOwner/ownershipPercentage'
import authorisedDetails from '../../GeneralApplication/Application/flow/businessOwner/authorisedDetails'
import birthdate from '../../GeneralApplication/Application/flow/businessOwner/birthdate'
import isAuthorised from '../../GeneralApplication/Application/flow/businessOwner/isAuthorised'
import isOwner from '../../GeneralApplication/Application/flow/businessOwner/isOwner'
import ssn from '../../GeneralApplication/Application/flow/businessOwner/ssn'
import debt from '../../GeneralApplication/Application/flow/finance/debt'
import howMuchCredit from '../../GeneralApplication/Application/flow/finance/howMuchCredit'
import revenue from '../../GeneralApplication/Application/flow/finance/revenue'
import preview from '../../GeneralApplication/Application/flow/review/preview'
import review from '../../GeneralApplication/Application/flow/review/review'
import coOwners from '../../GeneralApplication/Application/flow/coOwnerInfo/coOwners'
import arAdvanceRequestedLimit from '../../GeneralApplication/Application/flow/finance/arAdvanceRequestedLimit'
import agreement from '../../GeneralApplication/Application/flow/review/agreement'
import category from './businessInfo/category'
import businessName from './businessInfo/businessName'

// TODO: VK: Unified: Review
// TODO: VK: Unified: Remove extra type options from title, description (just for backward compatibility for refactoring time)

export interface IApplicationStepEditorTitle {
  text?: string | (() => string)
  wrapperStyle?: any
}

export interface IApplicationStepDescription {
  text?: string
  style?: any
}

export interface IApplicationStepEditor {
  title?: IApplicationStepEditorTitle | string | (() => string)
  description?: IApplicationStepDescription | string
  decidingQuestion?: boolean
  showFooterMessage?: boolean
  canMoveNext?: boolean
  canSkip?: boolean
  handleBack?: () => boolean
  component: FC<any> | ((props: any) => JSX.Element)
}

export function getApplicationStepEditor(path: string): IApplicationStepEditor {
  switch (path) {
    // Business Info
    case Steps.businessInfo.email.path:
      return email
    case Steps.businessInfo.category.path:
      return category
    case Steps.businessInfo.businessName.path:
      return businessName
    case Steps.businessInfo.trade.path:
      return trade
    case Steps.businessInfo.businessPhone.path:
      return businessPhone
    case Steps.businessInfo.businessAddress.path:
      return businessAddress
    case Steps.businessInfo.startDate.path:
      return startDate
    case Steps.businessInfo.type.path:
      return type
    case Steps.businessInfo.ein.path:
      return ein

    // Finance
    case Steps.finance.revenue.path:
      return revenue
    case Steps.finance.debt.path:
      return debt
    case Steps.finance.howMuchCredit.path:
      return howMuchCredit
    case Steps.finance.arAdvanceRequestedLimit.path:
      return arAdvanceRequestedLimit

    // Business Owner
    case Steps.businessOwner.isOwner.path:
      return isOwner
    case Steps.businessOwner.ownershipPercentage.path:
      return ownershipPercentage
    case Steps.businessOwner.isAuthorized.path:
      return isAuthorised
    case Steps.businessOwner.authorizedDetails.path:
      return authorisedDetails
    case Steps.businessOwner.address.path:
      return address
    case Steps.businessOwner.birthdate.path:
      return birthdate
    case Steps.businessOwner.ssn.path:
      return ssn

    // Co Owner Info
    case Steps.coOwnerInfo.coOwners.path:
      return coOwners

    // Bank
    case Steps.bank.details.path:
      return details

    // Review
    case Steps.review.review.path:
      return review
    case Steps.review.preview.path:
      return preview
    case Steps.review.agreement.path:
      return agreement

    default:
      throw new Error(`no editor for path: ${path}`)
  }
}
