import { ICompany, IInvoice, IOperation, IUser } from '../../models/types'
import {
  EInvoiceNotificationType,
  EInvoiceType,
  exceptions,
} from '@linqpal/models'
import moment, { Moment, MomentInput } from 'moment-timezone'
import { CustomerAccount, Invoice, InvoiceNote, Operation } from '../../models'
import {
  invoiceSchemaStatus,
  invoiceStatus,
  OPERATION_STATUS,
  OPERATION_TYPES,
} from '@linqpal/models/src/dictionaries'
import { emailService } from '../email.service'
import {
  sendInvoiceDraftNotifications,
  sendInvoicePlacedNotifications,
  sendInvoicePlacedToCreator,
} from './notifications'
import { checkIfCreditAppProcessing } from './checkIfCreditAppProcessing'
import { cancelLoanApplication } from '../loan/cancelLoanApplication'
import EmailBuilder from '../../helpers/EmailBuilder'
import AutoTradeCreditService from '../tradeCredit/autoTradeCredit.service'
import { QuoteService } from '../quote/quote.service'
import { QuoteNotifications } from '../quote/quoteNotifications.service'
import { ClientSession } from 'mongoose'
import { FactoringService } from '../factoring'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'

function toCSTDate(
  date: string | Moment | MomentInput | Date | undefined,
): string | null {
  if (!date) return null
  const dt = moment(date, 'MM/DD/YYYY')
  const dateString = dt.isValid() ? dt.format('YYYY-MM-DD') : date
  return moment(dateString).tz('America/Chicago').endOf('day').format()
}

export async function saveInvoice(
  data: Omit<
    Partial<IInvoice>,
    'invoice_date' | 'tax_amount' | 'invoice_due_date'
  > & {
    currentDate?: string
    createdBy?: string
    invoice_date?: Date
    invoice_due_date?: Date | string // TODO: VK: Review dates handling on saving invoice
  },
  company: ICompany,
  user: IUser,
  host: string,
  notify_draft: boolean,
  session: ClientSession | null = null, // TODO: VK: require session everywhere
) {
  let {
    id,
    tax_exempted,
    currentDate,
    invoiceNotificationType = EInvoiceNotificationType.BOTH,
    invoice_due_date,
    notes,
    customer_account_id,
    quote_number,
    ...rest
  } = data
  let invoice: IInvoice
  // TODO: VK: use single type for dates
  let invoiceDueDate: string | moment.Moment | undefined | Date =
    invoice_due_date
  const existingInvoice = !!id

  if (customer_account_id) {
    const customerAccount = await CustomerAccount.findById(customer_account_id)
    if (customerAccount?.isDeleted) {
      throw new exceptions.LogicalError(
        'Invoice can not be created or updated for deleted customer account',
      )
    }
  }

  const isQuote = rest.type === EInvoiceType.QUOTE

  if (isQuote) {
    // due date has no meaning for quotes,
    // but it's required and defaulted to current CST date in Invoice mst model
    // not sure if it's safe to make it optional
    // so set it here to future to avoid past due related jobs to handle it
    invoiceDueDate = moment().add(1000, 'years')
  } else if (!invoiceDueDate) {
    if (company.settings.dueDay) {
      invoiceDueDate = moment()
        .add(1, 'month')
        .set('date', company.settings.dueDay)
    } else {
      invoiceDueDate = moment().endOf('day')
    }
  }

  if (id) {
    const currentInvoice = await Invoice.findById(id)

    if (
      currentInvoice?.paymentDetails?.arAdvanceStatus &&
      currentInvoice.paymentDetails.arAdvanceStatus !==
        ArAdvanceStatus.NotApplied
    ) {
      invoice = currentInvoice
      // AR Advance invoice should not be allowed to updated (only draft ones).
      //But AR Advance inv still should be allowed to resend
    }
    {
      currentInvoice?.status === invoiceStatus.draft &&
        rest.status &&
        [(invoiceStatus.draft, invoiceStatus.placed)].find(
          (stat) => stat === rest.status,
        )

      const newPaymentDetails = await FactoringService.getUpdatedPaymentDetails(
        company,
        {
          id,
          type: currentInvoice?.type ?? EInvoiceType.INVOICE,
          total_amount: rest.total_amount ?? 0,
          customer_account_id: customer_account_id ?? '',
          status: rest.status ?? invoiceStatus.draft,
        },
      )

      invoice = (await Invoice.findOneAndUpdate(
        { _id: id, company_id: company._id },
        {
          ...rest,
          invoiceNotificationType,
          quote_number: quote_number ?? null,
          customer_account_id,
          invoice_date: toCSTDate(rest.invoice_date as Date),
          invoice_due_date: toCSTDate(invoiceDueDate),
          expiration_date: isQuote
            ? null
            : toCSTDate(rest.expiration_date as string),
          company_id: company._id,
          ...(newPaymentDetails && { paymentDetails: newPaymentDetails }),
        },
        { new: true },
      ))!
    }
  } else {
    let newPaymentDetails: any = null
    if (rest.status === invoiceStatus.draft) {
      newPaymentDetails = await FactoringService.getUpdatedPaymentDetails(
        company,
        {
          type: rest.type ?? EInvoiceType.INVOICE,
          id: 'new invoice',
          total_amount: rest.total_amount ?? 0,
          customer_account_id: customer_account_id ?? '',
          status: rest.status,
        },
      )
    }
    rest.company_id = company._id

    if (rest.supplierInvitationDetails) {
      rest.createdBy = 'contractor'
      rest.approved = false
      rest.status = invoiceSchemaStatus.draft
      rest.supplierInvitationDetails.userId = user._id.toString()
      rest.supplierInvitationDetails.email = (
        rest.supplierInvitationDetails?.email || ''
      ).toLowerCase()
      rest.company_id = ''
      rest.payer_id = company.id
    }

    const invoiceData: Partial<IInvoice> = {
      ...rest,
      ...(newPaymentDetails && { paymentDetails: newPaymentDetails }),
      quote_number,
      customer_account_id,
      invoice_date: toCSTDate(rest.invoice_date as Date) ?? undefined,
      invoice_due_date: toCSTDate(invoiceDueDate) ?? undefined,
      expiration_date: isQuote
        ? null
        : toCSTDate(rest.expiration_date as string),
      invoiceNotificationType: invoiceNotificationType,
    }

    invoice = await Invoice.create(invoiceData)
    id = invoice._id.toString()
  }

  if (notes && notes.length > 0) {
    for (const note of notes) {
      await InvoiceNote.create({
        invoiceId: id,
        message: note,
        sub: user.sub,
      })
    }
  }

  if (rest.status === invoiceStatus.placed && invoice.total_amount < 0) {
    await notifyOpsTeam(invoice, company)
  }

  if (rest.status === invoiceStatus.draft && notify_draft) {
    await sendInvoiceDraftNotifications(
      id,
      invoiceNotificationType,
      company,
      host,
    )
  }

  if (rest.status === invoiceStatus.placed) {
    await Operation.findOneAndUpdate(
      { owner_id: id },
      {
        date: moment().toDate(),
        type: OPERATION_TYPES.INVOICE.PAYMENT,
        amount: invoice.total_amount,
        status: OPERATION_STATUS.PLACED,
        metadata: { payee_id: company.id },
      } as IOperation,
      { new: true, upsert: true },
    )

    await sendInvoicePlacedToCreator(invoice)

    const [processingApp] = await checkIfCreditAppProcessing([id])

    if (existingInvoice && processingApp) {
      await cancelLoanApplication(processingApp._id?.toString(), null, false)
    }

    // can be overriden by factoring
    // TODO: VK: use single type for dates
    const originalDueDate = invoice_due_date
      ? typeof invoice_due_date === 'string'
        ? moment
            .tz(invoice_due_date, 'MM/DD/YYYY', 'America/Chicago')
            .endOf('day')
            .toDate()
        : moment(invoice_due_date).toDate()
      : undefined

    // ensure quote match is before AR Advance, so ARA-eligible invoice could be matched with ARA quote
    const isQuoteMatched = await QuoteService.tryMatchInvoiceToQuote(
      invoice,
      originalDueDate,
      host,
      session,
    )

    // notifications for quotes are handled by DE
    if (isQuoteMatched) return id

    const isArAdvanceStarted = await FactoringService.tryStartArAdvance(
      invoice,
      company,
      originalDueDate,
      session,
    )

    if (!isArAdvanceStarted) {
      const isAtcStarted = await AutoTradeCreditService.tryStartAutoTradeCredit(
        invoice,
        company,
      )

      if (isAtcStarted) return id

      if (invoice.type === EInvoiceType.QUOTE && !isAtcStarted) {
        await QuoteNotifications.quoteCreated(invoice, session)
        return id
      }
    }

    // no need to send any customer notifications for factoring quotes
    if (isQuote && isArAdvanceStarted) return

    // notifications for quotes / ATC are handled by decision engine
    await sendInvoicePlacedNotifications(
      id,
      [invoice.customer_account_id, ...invoice.contacts].filter(Boolean),
      currentDate,
      invoiceNotificationType,
      company,
      user,
      host,
      session,
    )
  }

  // const invoice_event = id ? 'Updated' : 'Created'
  // await emitInvoiceEvent(id, invoice_event)
  return id
}

// async function emitInvoiceEvent(
//   invoiceId: string,
//   eventType: 'Created' | 'Updated',
// ) {
//   try {
//     await AwsService.sendSQSMessage(
//       'invoice-syncback',
//       JSON.stringify({ invoiceId, eventType }),
//     )
//   } catch (e) {
//     console.error(e)
//   }
// }

async function notifyOpsTeam(invoice: IInvoice, company: ICompany) {
  const to =
    process.env.LP_MODE === 'prod'
      ? emailService.EMAILS.PRODUCT_OPERATION_TEAM
      : emailService.EMAILS.PRODUCT_OPERATION_TEAM_TEST

  const emailMessage = EmailBuilder.getSubjectAndBody({
    key: 'saveInvoiceOpsTeamNotification',
    data: {
      name: company.name,
      invoiceNumber: invoice.invoice_number,
      totalAmount: invoice.total_amount,
    },
  })

  await emailService.send({
    to,
    subject: emailMessage.subject,
    html: emailMessage.body,
  })
}
