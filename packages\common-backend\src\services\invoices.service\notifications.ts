import {
  getStateTimeZone,
  invoiceStatus,
  OPERATION_STATUS,
} from '@linqpal/models/src/dictionaries'
import { validateEmail } from '@linqpal/models/src/helpers/validations'
import { parsePhoneNumber } from 'libphonenumber-js'
import mongoose, { ClientSession } from 'mongoose'
import { getApiURL } from '../../helpers/common'
import {
  Company,
  CustomerAccount,
  Invoice,
  Notification,
  User,
  UserRole,
} from '../../models'
import { ICompany, ICustomerAccount, IInvoice, IUser } from '../../models/types'
import { emailService } from '../email.service'
import { EConnectorType, EInvoiceType } from '@linqpal/models'
import moment from 'moment-timezone'
import { stringifyUrl } from 'query-string'
import numbro from 'numbro'
import { referralService } from '../referral.service'
import Sms from '../sms.service'
import { findBuilderAccount } from './findBuilderAccount'
import { getRecei<PERSON>, Receiver } from './getReceivers'
import SmsBuilder from '../../helpers/SmsBuilder'
import EmailBuilder from '../../helpers/EmailBuilder'
import { getSalesRepAndTakenBy } from './getSalesRepAndTakenBy'
import { CompanyRepository } from '../../repositories/company.repository'
import { toCurrency } from '@linqpal/models/src/helpers/formatter'

async function referralCode(company: ICompany) {
  try {
    const referralData: string | { referralCode: string } =
      await referralService.getReferralCode({
        company,
        type: 'supplier',
      })
    if (typeof referralData === 'string') {
      return referralData
    } else if (typeof referralData?.referralCode === 'string') {
      return referralData?.referralCode
    }
  } catch (e) {
    console.warn('Failed to get referral code', e)
  }
  return ''
}

export async function getInvoiceLink(
  invoiceId: string,
  company: ICompany | null,
  host: string,
) {
  const link_params: Record<string, string> = { id: invoiceId }

  const referral_code = company ? await referralCode(company) : null
  if (referral_code) link_params.REFERRALCODE = referral_code

  return stringifyUrl({
    url: `${host}/i/r`,
    query: link_params,
  })
}

export function getInvoiceIntegrationLink(invoiceId: string, host: string) {
  const link_params: Record<string, string> = { invoiceId: invoiceId }

  return stringifyUrl({
    url: `${host}/integration-payment`,
    query: link_params,
  })
}

async function sendSms(receivers: { phone?: string }[], message: string) {
  const _receivers = receivers.filter((r) => r.phone) as { phone: string }[]
  if (_receivers.length === 0) return
  const results = await Promise.allSettled(
    _receivers.map(async (r) => {
      const phoneNumber = parsePhoneNumber(r.phone, 'US').number.toString()
      await Sms.send(phoneNumber, message)
    }),
  )
  results
    .filter((r) => r.status === 'rejected')
    .forEach((r) => {
      console.log('failed to send sms', (r as PromiseRejectedResult).reason)
    })
}

export async function sendInvoicePaymentNotification(
  invoiceId = '',
  paymentStatus: string,
) {
  if (!invoiceId) return
  let invoice
  try {
    invoice = await Invoice.findById(invoiceId)
  } catch (e) {
    console.error('Invoice not found with ID: ' + invoiceId)
  }
  if (!invoice) return

  let subject, html, to, supplier, contractor

  try {
    if (invoice.company_id) {
      supplier = await Company.findById(invoice.company_id)
    } else if (invoice.supplierInvitationDetails) {
      supplier = invoice.supplierInvitationDetails
    }
    if (supplier && supplier.email) {
      to = supplier.email
    }

    if (invoice.customer_account_id && invoice.customer_account_id.trim()) {
      contractor = await CustomerAccount.findById(invoice.customer_account_id)
    } else if (invoice.payer_id) {
      contractor = await Company.findById(invoice.payer_id)
    }

    const payerName = contractor?.name
    if (!to || !validateEmail(to) || !payerName) return

    const link = getApiURL() + '/login'
    const processingStatusMessage = EmailBuilder.getSubjectAndBody({
      key: 'invoicePaymentProcessingNotification',
      data: {
        name: payerName,
        invoiceNumber: invoice.invoice_number,
        url: link,
      },
    })
    const successStatusMessage = EmailBuilder.getSubjectAndBody({
      key: 'invoicePaymentSuccessNotification',
      data: {
        name: payerName,
        invoiceNumber: invoice.invoice_number,
      },
    })
    const failStatusMessage = EmailBuilder.getSubjectAndBody({
      key: 'invoicePaymentFailNotification',
      data: {
        name: payerName,
        invoiceNumber: invoice.invoice_number,
        url: link,
      },
    })

    switch (paymentStatus) {
      case OPERATION_STATUS.PROCESSING:
        subject = processingStatusMessage.subject
        html = `<p>${processingStatusMessage.body}</p>`
        break
      case OPERATION_STATUS.SUCCESS:
        subject = successStatusMessage.subject
        html = `${successStatusMessage.body}`
        break
      case OPERATION_STATUS.FAIL:
        subject = failStatusMessage.subject
        html = `${failStatusMessage.body}`
        break
    }
    if (!subject) return

    await emailService.send({ to, subject, html })
  } catch (e) {
    console.error(e)
  }
}

async function sendEmail(
  receivers: { email?: string }[],
  subject: string,
  html: string,
) {
  const _receivers = receivers.filter((r) => r.email) as { email: string }[]
  if (_receivers.length === 0) return
  const results = await Promise.allSettled(
    _receivers.map(async (r) => {
      await emailService.send({ to: r.email, subject, html })
    }),
  )
  results
    .filter((r) => r.status === 'rejected')
    .forEach((r) => {
      console.log('failed to send email', (r as PromiseRejectedResult).reason)
    })
}

type NOTIFICATION_TYPE = 'Both' | 'TextInvoice' | 'EmailInvoice'

function getOffset(invoice: any) {
  const state =
    invoice.draft?.rest?.registered?.items?.[0]?.content?.state || 'Maryland'
  return getStateTimeZone(state)
}

export function firstNotifications(
  key: [boolean, boolean],
  companyName: string,
  invoice: IInvoice,
  options: any = {},
) {
  let documentType: string
  switch (invoice.type) {
    case EInvoiceType.QUOTE:
      documentType = 'a quote'
      if (options.updated) {
        documentType = 'an updated quote'
      }
      break
    default:
      documentType = 'a payment request'
      break
  }

  const dueDate = moment(invoice.invoice_due_date).format('MM/DD/YYYY')

  const notifications = [
    [
      [false, false].toString(),
      SmsBuilder.getMessage({
        key: 'firstNotificationsSent',
        data: {
          name: companyName,
          documentType,
          dueDate,
        },
      }),
    ],
    [
      [false, true].toString(),
      SmsBuilder.getMessage({
        key: 'firstNotificationsExpiredToday',
        data: {
          name: companyName,
          documentType,
        },
      }),
    ],
    [
      [true, false].toString(),
      SmsBuilder.getMessage({
        key: 'firstNotificationsDueToday',
        data: {
          name: companyName,
          documentType,
        },
      }),
    ],
    [
      [true, true].toString(),
      SmsBuilder.getMessage({
        key: 'firstNotificationsSent',
        data: {
          name: companyName,
          documentType,
          dueDate,
        },
      }),
    ],
  ]
  return Object.fromEntries(notifications)[key.toString()] + ' '
}

export function getSms(
  invoice: IInvoice,
  link: string,
  date: string | undefined,
  options: any = {},
) {
  const offset = getOffset(invoice)

  const dueDate = moment(invoice.invoice_due_date)
    .utcOffset(offset)
    .startOf('day')
  const currentDate = (date ? moment(date) : moment())
    .utcOffset(offset)
    .startOf('day')
  const expireDate = invoice.expiration_date
    ? moment(invoice.expiration_date).utcOffset(offset).startOf('day')
    : null

  const diffInvoiceDue = currentDate.diff(dueDate, 'days')
  const diffExpireDue = expireDate ? currentDate.diff(expireDate, 'days') : null

  const companyName =
    invoice.company?.name || invoice.company?.legalName || 'Undefined supplier'

  return (
    firstNotifications(
      [diffInvoiceDue === 0, expireDate ? diffExpireDue === 0 : false],
      companyName,
      invoice,
      options,
    ) + link
  )
}

async function sendInvoiceSMS(
  invoiceNotificationType: NOTIFICATION_TYPE,
  invoice: IInvoice,
  receiversWithPhone: any[],
  currentDate: string | undefined,
  options: any = {},
) {
  if (
    invoiceNotificationType !== 'TextInvoice' &&
    invoiceNotificationType !== 'Both'
  )
    return

  if (!receiversWithPhone.length) return

  const results = await Promise.allSettled(
    receiversWithPhone.map(async (receiver) => {
      const phoneNumber = parsePhoneNumber(
        receiver.phone,
        'US',
      ).number.toString()

      const account_link = receiver.link
      const smsText = getSms(invoice, account_link, currentDate, options)
      try {
        await Sms.send(phoneNumber, smsText)
      } catch (e) {
        console.log('sms send error', e)
      }
    }),
  )
  results.forEach((result) => {
    if (result.status === 'rejected') {
      console.warn(result.reason)
    }
  })
}

async function sendInvoiceNotification(
  invoice: IInvoice,
  company: ICompany,
  receivers: any[],
) {
  const existingNotification = await Notification.findOne({
    'metadata.alertType': 'sent',
    'metadata.invoice_id': invoice._id,
  })

  const amount = numbro(invoice.total_amount).format({
    thousandSeparated: true,
    mantissa: 2,
  })

  const content = [
    `${company.name} sent you`,
    existingNotification ? 'an updated' : 'a',
    `payment request of $${amount}`,
  ]

  if (invoice.expiration_date) {
    const date = moment(invoice.expiration_date).format('MM/DD/YYYY')
    content.push(`that expires on ${date}`)
  } else {
    const date = moment(invoice.invoice_due_date).format('MM/DD/YYYY')
    content.push(`that is due on ${date}`)
  }

  const results = await Promise.allSettled(
    receivers.map(async (receiver) => {
      let customerCompany: { _id: string }[] | undefined
      if (receiver.phone) {
        customerCompany = await findBuilderAccount(
          parsePhoneNumber(receiver.phone, 'US').number as string,
        )
      }
      if (
        (!customerCompany || customerCompany.length === 0) &&
        receiver.email
      ) {
        customerCompany = await findBuilderAccount(receiver.email)
      }
      await Notification.findOneAndUpdate(
        {
          'metadata.alertType': 'sent',
          'metadata.invoice_id': invoice._id,
        },
        {
          $set: {
            sender: {
              company_id: invoice.company_id,
              user_id: '',
            },
            receiver: {
              company_id: customerCompany![0]?._id.toString(),
              user_id: '',
            },
            content: content.join(' '),
            type: 'INVOICES',
            metadata: {
              alertType: 'sent',
              invoice_id: invoice._id,
              dueDate: invoice.invoice_due_date,
              expiryDate: invoice.expiration_date,
              companyName: company.name,
              amount: invoice.total_amount,
            },
            isRead: false,
            isViewed: false,
          },
          $setOnInsert: { createdAt: new Date() },
        },
        { new: true, upsert: true },
      )
    }),
  )
  results.forEach((result) => {
    if (result.status === 'rejected') {
      console.warn(result.reason)
    }
  })
}

export async function notifyOperations(
  invoice: IInvoice,
  senderName: string | null | undefined,
  receiver: Receiver,
) {
  const receiverName = receiver.name || receiver.fullName
  const emailMessage = EmailBuilder.getSubjectAndBody({
    key: 'notifyOperations',
    data: {
      name: senderName,
      invoiceNumber: invoice.invoice_number,
      totalAmount: toCurrency(invoice.total_amount),
      invoiceDueDate: moment(invoice.invoice_due_date)
        .tz('America/Chicago')
        .format('MM/DD/YYYY'),
      receiverName,
      email: receiver.email,
      phone: receiver.phone,
    },
  })

  const to =
    process.env.LP_MODE === 'prod'
      ? emailService.EMAILS.PRODUCT_OPERATION_TEAM
      : emailService.EMAILS.PRODUCT_OPERATION_TEAM_TEST

  await emailService.send({
    to,
    subject: emailMessage.subject,
    html: emailMessage.body,
  })
}

async function sendInvoiceEmail(
  invoiceNotificationType: NOTIFICATION_TYPE,
  invoice: IInvoice,
  company: ICompany,
  currentUser: IUser | null,
  receiversWithEmail: Receiver[],
) {
  if (
    invoiceNotificationType !== 'EmailInvoice' &&
    invoiceNotificationType !== 'Both'
  )
    return

  if (!receiversWithEmail.length) return

  const results = await Promise.allSettled(
    receiversWithEmail.map(async (receiver) => {
      const currentUserName = !currentUser
        ? ''
        : currentUser.firstName || currentUser.lastName
        ? [currentUser.firstName, currentUser.lastName]
            .filter(Boolean)
            .join(' ')
            .trim()
        : currentUser.name || ''
      const companyName = company?.name || company?.legalName || 'N/A'
      const dynamic_template_data = {
        link: receiver.link,
        firstName: receiver.firstName || currentUserName,
        companyName,
        supplierFirstName: currentUser?.firstName || '',
        invitedBy: currentUserName || companyName,
        email: currentUser?.email || company?.email,
        phone: company.phone || currentUser?.phone,
        dueDate: moment(invoice.invoice_due_date).format('MM/DD/YYYY'),
        logoUrl: company?.settings?.email?.logoUrl,
      }

      const from = company?.settings?.email?.senderEmail // if it doesn't exist, default email will be used
      const templateId =
        company?.settings?.email?.sendInvoiceTemplate ||
        emailService.TEMPLATES.SEND_INVOICE

      const params = {
        from,
        to: receiver.email,
        templateId,
        dynamic_template_data,
      }
      await emailService.send(params)
    }),
  )
  results.forEach((result) => {
    if (result.status === 'rejected') {
      console.warn(result.reason)
    }
  })
}

export async function sendInvoiceNotifications(
  invoice: IInvoice,
  customerAccounts: ICustomerAccount[],
  currentDate: string | undefined,
  invoiceNotificationType: NOTIFICATION_TYPE,
  company: ICompany,
  user: IUser | null,
  host: string,
  options = {},
) {
  if (invoice.status !== invoiceStatus.draft) {
    const { emailReceivers, smsReceivers } = await getReceivers(
      customerAccounts,
      invoice.id,
      company,
      host,
    )

    if (invoice.payersInfo?.length) {
      const link = getInvoiceIntegrationLink(invoice.id, host)

      invoice.payersInfo.forEach((payerInformation: any) => {
        const receiver: Receiver = {
          email: payerInformation.emailAddress
            ? payerInformation.emailAddress.toLowerCase()
            : '',
          phone: payerInformation.cellPhoneNumber ?? '',
          firstName: payerInformation.firstName ?? '',
          fullName: [
            payerInformation.firstName ?? '',
            payerInformation.lastName ?? '',
          ]
            .filter(Boolean)
            .join(' ')
            .trim(),
          customerAccountId: invoice.customer_account_id,
          link,
        }

        if (receiver.email && !emailReceivers.has(receiver.email)) {
          emailReceivers.set(receiver.email, receiver)
        }

        if (receiver.phone && !smsReceivers.has(receiver.phone)) {
          smsReceivers.set(receiver.phone, receiver)
        }
      })
    }

    const uniqueEmailReceivers = emailReceivers.size
      ? Array.from(emailReceivers.values())
      : []

    const uniqueSmsReceivers = smsReceivers.size
      ? Array.from(smsReceivers.values())
      : []

    if (
      invoice?.connector?.connector_type !== EConnectorType.Generic ||
      (invoice?.connector?.connector_type === EConnectorType.Generic &&
        invoice?.customer_account_id)
    ) {
      const results = await Promise.allSettled([
        sendInvoiceSMS(
          invoiceNotificationType,
          invoice,
          uniqueSmsReceivers,
          currentDate,
          options,
        ),
        sendInvoiceNotification(invoice, company, [
          ...uniqueEmailReceivers,
          ...uniqueSmsReceivers,
        ]),
        sendInvoiceEmail(
          invoiceNotificationType,
          invoice,
          company,
          user,
          uniqueEmailReceivers,
        ),
      ])

      results.forEach((result) => {
        if (result.status === 'rejected') {
          console.warn(result.reason)
        }
      })
    }

    const senderName =
      company?.name || company?.legalName || user?.firstName || user?.lastName
    await notifyOperations(
      invoice,
      senderName,
      uniqueEmailReceivers[0] || uniqueSmsReceivers[0],
    )
  }
}

export async function sendInvoicePlacedNotifications(
  invoiceId: string,
  customerAccountsId: string[],
  currentDate: string | undefined,
  invoiceNotificationType: NOTIFICATION_TYPE,
  company: ICompany,
  user: IUser,
  host: string,
  session: ClientSession | null,
) {
  const invoiceDetails = await Invoice.findById(invoiceId).session(session)
  const customerAccounts = await CustomerAccount.find({
    _id: mongoose.trusted({ $in: customerAccountsId }),
  }).session(session)

  if (invoiceDetails) {
    invoiceDetails.company = company

    await sendInvoiceNotifications(
      invoiceDetails,
      customerAccounts,
      currentDate,
      invoiceNotificationType,
      company,
      user,
      host,
    )
  }
}

export async function sendInvoicePlacedToCreator(invoice: IInvoice) {
  const customerAccount = await CustomerAccount.findById(
    invoice.customer_account_id,
  )
  const email_addresses: string[] = await getPaymentNotificationReceivers(
    invoice,
  )

  const receivers = email_addresses.map((email) => ({
    email,
  })) as { email?: string }[]

  const emailMessage = EmailBuilder.getSubjectAndBody({
    key: 'invoicePlacedCreatorNotification',
    data: { name: customerAccount?.name || 'customer' },
  })
  await sendEmail(receivers, emailMessage.subject, emailMessage.body).catch(
    (e) => {
      console.log('failed to send email', e)
    },
  )
  // await sendSms(receivers, message).catch((e) => {
  //   console.log('failed to send SMS', e)
  // })
}

export async function sendInvoiceDraftNotifications(
  invoiceId: string,
  invoiceNotificationType: string,
  company: ICompany,
  host: string,
) {
  const link = `${host}/getpaid/invoices/list`
  //const link = `${host}/getpaid/invoices/list/${invoiceId}`

  const roles = await UserRole.find({
    company_id: company._id,
    role: 'Owner',
  }).exec()
  const users = await User.find({
    sub: mongoose.trusted({ $in: roles.map((r) => r.sub) }),
  }).exec()

  const receivers = users.map((u) => ({
    email: u.email,
    phone: u.phone,
    fullName: [u.firstName, u.lastName].filter(Boolean).join(' '),
  })) as { email?: string; phone?: string; fullName: string }[]

  if (
    invoiceNotificationType === 'TextInvoice' ||
    invoiceNotificationType === 'Both'
  ) {
    const message = SmsBuilder.getMessage({
      key: 'invoiceDraftNotifications',
      data: { url: link },
    })
    console.log('invoice sms', message)
    await sendSms(receivers, message).catch((e) => {
      console.log('failed to send sms', e)
    })

    const emailMessage = EmailBuilder.getSubjectAndBody({
      key: 'invoiceDraftNotifications',
      data: { url: link },
    })
    const html = `<div>${emailMessage.body}</div>`

    console.log('invoice email', emailMessage.subject, html)
    await sendEmail(receivers, emailMessage.subject, html).catch((e) => {
      console.log('failed to send email', e)
    })
  }
}

export const getPaymentNotificationReceivers = async (invoice: IInvoice) => {
  const customer = await CustomerAccount.findById(invoice.customer_account_id)
  // Receivers from Supplier company
  const emails: string[] = []

  //1. Check if a Sales Representative / Taken By user exists
  const salesRepTakenByEmails = await getSalesRepAndTakenBy(invoice, customer)
  emails.push(...salesRepTakenByEmails)

  //2. Other users from supplier company who have Payment notification setting enabled
  const [otherUserContacts, supplier] = await Promise.all([
    CompanyRepository.getEmailContacts(invoice?.company_id),
    Company.findById(invoice?.company_id),
  ])

  otherUserContacts.forEach((contact) => {
    if (!emails.includes(contact.email)) emails.push(contact.email)
  })

  if (supplier?.email && !emails.includes(supplier.email)) {
    emails.push(supplier.email)
  }

  return emails
}
