import { TFunction } from 'react-i18next'
import { EInvoiceType } from '@linqpal/models'

export const getReceivableLocalization = (
  type: EInvoiceType,
  t: TFunction<'global'>,
) => {
  if (type === EInvoiceType.STATEMENT)
    throw new Error(`Unsupported localization type ${type}"`)

  if (!type) type = EInvoiceType.INVOICE

  return {
    label: t(`Receivables.${type}.label`),
    shortLabel: t(`Receivables.${type}.short-label`),
    number: t(`Receivables.${type}.number`),
    status: t(`Receivables.${type}.status`),
    numberValidation: t(`Receivables.${type}.number-validation-message`),
    date: t(`Receivables.${type}.date`),
    listTitle: t(`Receivables.${type}.list-title`),
    newReceivable: t(`Receivables.${type}.new`),
    addReceivable: t(`Receivables.${type}.add`),
    addNewReceivable: t(`Receivables.${type}.add-new`),
    createReceivable: t(`Receivables.${type}.create`),
    createNewReceivable: t(`Receivables.${type}.create-new`),
    editReceivable: t(`Receivables.${type}.edit`),
    uploadReceivable: t(`Receivables.${type}.upload`),
    previewReceivable: t(`Receivables.${type}.preview`),
    cancelReceivable: t(`Receivables.${type}.cancel`),
    sendReceivable: t(`Receivables.${type}.send`),
    resendReceivable: t(`Receivables.${type}.resend`),
    deleteReceivable: t(`Receivables.${type}.delete`),
    approveReceivable: t(`Receivables.${type}.approve`),
    rejectReceivable: t(`Receivables.${type}.reject`),
    sendNewReceivable: t(`Receivables.${type}.send-new`),
    emailReceivable: t(`Receivables.${type}.email`),
    textReceivable: t(`Receivables.${type}.text`),
    uploadingReceivable: t(`Receivables.${type}.uploading`),
    invalidDateError: t(`Receivables.${type}.invalid-date-error`),
    sendMethodTitle: t(`Receivables.${type}.send-method-title`),
    filterPlaceholder: t(`Receivables.${type}.filter-placeholder`),
    uploadDescription: t(`Receivables.${type}.upload-description`),
    bigFileSizeError: t(`Receivables.${type}.big-file-size-error`),
    receivablePlaced: t(`Receivables.${type}.receivable-placed`),
    receivableDraftPlaced: t(`Receivables.${type}.receivable-draft-placed`),
    selectReceivablePrompt: t(`Receivables.${type}.select-receivable-prompt`),
    noReceivablesTitle: t(`Receivables.${type}.no-receivables-title`),
    clickToCreateNew: t(`Receivables.${type}.click-to-create-new`),
  }
}
