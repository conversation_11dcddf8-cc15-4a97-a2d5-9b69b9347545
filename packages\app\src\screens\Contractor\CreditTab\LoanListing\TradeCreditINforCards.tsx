import { StyleSheet, View } from 'react-native'
import { BtLink, BtPlainText } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import { toCurrency, toPercentage } from '@linqpal/models/src/helpers/formatter'
import React, { FC } from 'react'
import RootStore from '../../../../store/RootStore'
import LegalFiles from '../../../../utils/helpers/legalFiles'

export const TradeCreditInfoCards: FC = () => {
  const { t } = useTranslation('global')

  const downPaymentSettings =
    RootStore.userStore?.company?.settings?.downPaymentDetails

  const depositSettings = RootStore.userStore?.company?.settings?.depositDetails

  if (!downPaymentSettings?.isRequired && !depositSettings?.isSecured) {
    return null
  }

  // TODO: VK: Reuse OverviewBox
  return (
    <View style={styles.container}>
      {downPaymentSettings?.isRequired && (
        <View style={styles.card}>
          <BtPlainText style={styles.title}>
            {t('LoanListing.down-payment-title')}
          </BtPlainText>
          <BtPlainText style={styles.text}>
            {t('LoanListing.down-payment-hint')}
          </BtPlainText>
          <View style={[styles.bottomWrapper, styles.downPaymentWrapper]}>
            <BtPlainText style={styles.percentage}>
              {toPercentage(downPaymentSettings.downPaymentPercentage)}
            </BtPlainText>
            <BtLink
              onPress={LegalFiles.downloadDownPaymentInstructions}
              textStyle={styles.link}
              title={t('LoanListing.download-wire-instructions')}
            />
          </View>
        </View>
      )}

      {depositSettings?.isSecured && (
        <View style={styles.card}>
          <BtPlainText style={styles.title}>
            {t('LoanListing.security-deposit-title')}
          </BtPlainText>
          <BtPlainText style={styles.text}>
            {t('LoanListing.security-deposit-hint')}
          </BtPlainText>
          <View style={[styles.bottomWrapper, styles.depositWrapper]}>
            <BtPlainText style={styles.percentage}>
              {toCurrency(depositSettings.depositAmount)}
            </BtPlainText>
            {depositSettings.isDepositPaid && (
              <BtPlainText style={styles.badge}>
                {t('LoanListing.security-deposit-paid')}
              </BtPlainText>
            )}
          </View>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    margin: 16,
    flexDirection: 'row',
    gap: 16,
    flexWrap: 'wrap',
  },
  card: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E6E8EA',
    borderRadius: 12,
    minWidth: 280,
    maxWidth: 500,
    padding: 20,
  },
  title: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 6,
  },
  text: {
    fontSize: 12,
    fontWeight: '600',
  },
  percentage: {
    fontSize: 24,
    fontWeight: '700',
  },
  link: {
    fontSize: 14,
    fontWeight: '700',
    color: '#00A0F3',
  },
  bottomWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingTop: 16,
    marginTop: 'auto',
  },
  downPaymentWrapper: {
    justifyContent: 'space-between',
  },
  depositWrapper: {
    alignItems: 'center',
  },
  badge: {
    fontSize: 12,
    fontWeight: '500',
    color: '#25B883',
    backgroundColor: '#25B8831A',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 5,
  },
})
