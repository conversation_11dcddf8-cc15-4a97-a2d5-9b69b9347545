import React, { FC, useEffect } from 'react'
import {
  CButton,
  CCol,
  CForm,
  CFormGroup,
  CInput,
  CInputCheckbox,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CRow,
  CSelect,
} from '@coreui/react'
import 'react-datepicker/dist/react-datepicker.css'
import { Instance } from 'mobx-state-tree'
import LoanStatusDetailsStore from '../LoanStatusDetailsStore'
import { observer } from 'mobx-react'
import { PaymentFrequency } from '../LoanCustomPaymentPlanStore'
import { fb } from '../../../service'
import { CCurrencyInput } from '../../../components/CCurrencyInput'

type NewPaymentPlanProps = {
  onSuccess: () => void
  onClose: () => void
  store: Instance<typeof LoanStatusDetailsStore>
}

const DATE_FORMAT = 'YYYY-MM-DD'

const NewPaymentPlanModal: FC<NewPaymentPlanProps> = observer((props) => {
  const { onClose, onSuccess, store } = props
  const { customPaymentPlan: paymentPlan } = store

  useEffect(() => {
    paymentPlan.setAmount(store.loanPrincipalBalance)
    paymentPlan.setLoanId(store.item?._id || '')
  }, [paymentPlan, store.item?._id, store.loanPrincipalBalance])

  const onApplyNewPlan = async () => {
    await paymentPlan.submit(
      store.item?.lms?.loanReceivables,
      store.item?.lms?.id || '',
      fb.auth().currentUser?.uid || '',
      () => {
        onClose()
        onSuccess()
      },
    )
  }

  return (
    <CModal show={true} onClose={onClose} style={{ width: 630, padding: 20 }}>
      <CModalHeader>
        <CModalTitle>New Payment Plan</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CForm>
          <CFormGroup>
            <CLabel className="text-value">
              Please select Loan Payment Frequency
            </CLabel>
            <CSelect
              color="primary"
              placeholder="Payment types"
              value={paymentPlan.frequency}
              onChange={(args: any) =>
                paymentPlan.setFrequency(args.target.value)
              }
            >
              {Object.values(PaymentFrequency).map((item) => (
                <option value={item} key={item}>
                  {item}
                </option>
              ))}
            </CSelect>
          </CFormGroup>
          <CFormGroup>
            <CLabel className="text-value">
              How many scheduled payments should be created?
            </CLabel>
            <CInput
              type="number"
              min={1}
              max={
                paymentPlan.frequency === PaymentFrequency.Monthly
                  ? 6
                  : paymentPlan.frequency === PaymentFrequency.Weekly
                  ? 25
                  : undefined
              }
              step={1}
              value={paymentPlan.paymentsCount || undefined}
              onChange={(args: any) =>
                paymentPlan.setPaymentsCount(Number(args.target.value))
              }
            />
          </CFormGroup>
          <CFormGroup variant="checkbox" className="pl-4">
            <CInputCheckbox
              id="payment_plan_modal_equal_payments"
              checked={paymentPlan.equalPayments}
              onChange={(args: any) => {
                paymentPlan.setEqualPayments(args.target.checked)
              }}
            />
            <CLabel
              variant="checkbox"
              htmlFor="payment_plan_modal_equal_payments"
            >
              Equal Payments
            </CLabel>
          </CFormGroup>
          {paymentPlan.frequency !== PaymentFrequency.Custom && (
            <CFormGroup className="mt-3">
              <CLabel className="text-value">
                What is the start date of the payment plan?
              </CLabel>
              <CInput
                type="date"
                value={paymentPlan.startDate?.format(DATE_FORMAT)}
                invalid={!paymentPlan.isValidStartDate()}
                min={paymentPlan.minDate().format(DATE_FORMAT)}
                max={paymentPlan.maxDate().format(DATE_FORMAT)}
                onChange={(args: any) =>
                  paymentPlan.setStartDate(args.target.value)
                }
              />
            </CFormGroup>
          )}
          {paymentPlan.installments.length > 0 && (
            <CFormGroup>
              <CModalTitle className="mt-3">New Schedule Preview</CModalTitle>
              <CRow className="font-weight-bold mt-3">
                <CCol sm={1} />
                <CCol className="pl-0" sm={6}>
                  Extension Fee
                </CCol>
              </CRow>
              <CRow className="mt-2">
                <CCol
                  sm={1}
                  className="col-form-label"
                  style={{
                    maxWidth: 60,
                    textAlign: 'center',
                    paddingRight: 0,
                    paddingLeft: 27,
                  }}
                >
                  <CInputCheckbox
                    checked={paymentPlan.useExtensionFee}
                    onChange={(args: any) => {
                      paymentPlan.setUseExtensionFee(args.target.checked)
                    }}
                  />
                </CCol>
                <CCol className="p-0 pr-2" sm={6}>
                  <CInput
                    type="date"
                    min={paymentPlan.minDate().format(DATE_FORMAT)}
                    max={paymentPlan.maxDate().format(DATE_FORMAT)}
                    invalid={!paymentPlan.isValidExtensionFeeDate()}
                    disabled={!paymentPlan.useExtensionFee}
                    value={paymentPlan.extensionFeeDate?.format(DATE_FORMAT)}
                    onChange={(args: any) =>
                      paymentPlan.setExtensionFeeDate(args.target.value)
                    }
                  />
                </CCol>
                <CCol className="pl-0 flex-row" sm={5}>
                  <CCurrencyInput
                    amount={paymentPlan.extensionFeeAmount}
                    disabled={!paymentPlan.useExtensionFee}
                    onChange={paymentPlan.setExtensionFeeAmount}
                  />
                </CCol>
              </CRow>
              <CRow className="font-weight-bold mt-2">
                <CCol className="text-center" sm={1}>
                  #
                </CCol>
                <CCol className="pl-0" sm={6}>
                  New Date
                </CCol>
                <CCol className="pl-0" sm={5}>
                  New Amount
                </CCol>
              </CRow>
              {store.customPaymentPlan.installments.map(
                (installment, index) => (
                  <CRow className={'mt-2'} key={installment.key}>
                    <CCol
                      sm={1}
                      className="col-form-label"
                      style={{ maxWidth: 60, textAlign: 'center' }}
                    >
                      {index + 1}
                    </CCol>
                    <CCol sm={6} className={'p-0 pr-2'}>
                      <CInput
                        disabled={
                          paymentPlan.frequency !== PaymentFrequency.Custom
                        }
                        type="date"
                        inputMode={'text'}
                        invalid={!installment.isValidDate()}
                        min={installment.minDate.format(DATE_FORMAT)}
                        max={installment.maxDate.format(DATE_FORMAT)}
                        value={installment.date?.format(DATE_FORMAT)}
                        onChange={(args: any) =>
                          paymentPlan.setInstallmentDate(
                            installment,
                            args.target.value,
                          )
                        }
                      />
                    </CCol>
                    <CCol sm={5} className={'pl-0 flex-row'}>
                      <CCurrencyInput
                        amount={installment.amount}
                        disabled={paymentPlan.equalPayments}
                        onChange={installment.setAmount}
                      />
                    </CCol>
                  </CRow>
                ),
              )}
              <CRow className="mt-2 font-weight-bold">
                <CCol sm={1} />
                <CCol sm={6} className="text-right" style={{ paddingRight: 8 }}>
                  Outstanding Principal Amount
                </CCol>
                <CCol sm={5} style={{ paddingLeft: 12 }}>
                  {paymentPlan.totalOutstandingFormatted()}
                </CCol>
              </CRow>
              {!paymentPlan.equalPayments && (
                <CRow className="mt-2">
                  <CCol sm={1} />
                  <CCol
                    sm={6}
                    className="text-right"
                    style={{ paddingRight: 8 }}
                  >
                    Amount due
                  </CCol>
                  <CCol
                    sm={5}
                    style={{ paddingLeft: 12 }}
                    className={
                      paymentPlan.amountDue() ? 'text-danger' : 'text-success'
                    }
                  >
                    {paymentPlan.amountDueFormatted()}
                  </CCol>
                </CRow>
              )}
            </CFormGroup>
          )}
          <CFormGroup className="mt-2">
            <CLabel className="text-value">
              Enter reason for loan rescheduling*
            </CLabel>
            <CInput
              value={paymentPlan.reschedulingReason}
              onChange={(args: any) =>
                paymentPlan.setReschedulingReason(args.target.value)
              }
            />
          </CFormGroup>
        </CForm>
      </CModalBody>

      <CModalFooter className="pt-3 pb-3">
        <CButton onClick={onClose}>Cancel</CButton>
        <CButton
          color="primary"
          disabled={!paymentPlan.canSubmit()}
          onClick={onApplyNewPlan}
        >
          Apply New Plan
        </CButton>
      </CModalFooter>
    </CModal>
  )
})

export default NewPaymentPlanModal
