import CIcon from '@coreui/icons-react'
import {
  CAlert,
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCardTitle,
  CCol,
  CDataTable,
  CFormGroup,
  CInput,
  CInputGroup,
  CInputGroupAppend,
  CInputGroupText,
  CLabel,
  CRow,
  CSpinner,
} from '@coreui/react'
import { routes } from '@linqpal/models'
import { LOAN_REPAYMENT_STATUS } from '@linqpal/models/src/dictionaries/loanStatuses'
import classNames from 'classnames'
import { observer } from 'mobx-react'
import moment from 'moment-timezone'
import numbro from 'numbro'
import React, { FC, useCallback, useEffect, useState } from 'react'
import LoanSkipFinalPayment from './LoanSkipFinalPayment'
import LoanStatusDetailsStore from './LoanStatusDetailsStore'
import LmsPaymentsTable from './LmsPaymentsTable'
import { CurrentUser, ILoanStore } from './models'
import { GetLabelWithInfo } from './LoanStatus'
import { NotesTable } from './NotesTable'
import { ReceivablesTable } from './ReceivablesTable'
import { AmountCell } from './components/AmountCell'
import { fb } from '../../service'
import ChangeLoanStatusModal from './modals/ChangeLoanStatusModal'
import { toPercentage } from '@linqpal/models/src/helpers/formatter'

const preventInputOnChange = (e) => {
  e.preventDefault()
}
export default observer(function LoanStatusDetails({
  match,
  history,
}): React.ReactElement {
  const currentUser: CurrentUser | null = fb.auth().currentUser
  const store = LoanStatusDetailsStore
  const { loading, item, message, fetchData } = store
  const { id } = match.params
  const [visible, setVisible] = useState<boolean>(false)

  const onChangeLoanStatusModalOpen = () => setVisible(true)
  const onChangeLoanStatusModalClose = () => setVisible(false)

  const fetchDetails = useCallback(() => fetchData(id).cancel, [fetchData, id])

  useEffect(() => fetchDetails(), [fetchDetails])

  return (
    <div>
      <CCard>
        <CCardHeader className="d-flex flex-row align-items-center justify-content-sm-between">
          {loading ? (
            <CSpinner size="sm" className="mr-3" />
          ) : (
            <CButton className="mr-3" onClick={() => history.goBack()}>
              <CIcon name="cil-arrow-left" />
            </CButton>
          )}
          <CCardTitle className="mb-0">
            Loan status details
            {store.itemIssueDate ? ' - ' + store.itemIssueDate : ''}
          </CCardTitle>
          <CButton
            color="danger"
            className="mb-0 mr-2"
            onClick={onChangeLoanStatusModalOpen}
          >
            Change Loan Status
          </CButton>
        </CCardHeader>
        <CCardBody className="position-relative">
          <CAlert show={!!message} color="warning">
            {message}
          </CAlert>
          <CRow>
            <CCol xs={12} md={10} lg={8}>
              <CFormGroup row>
                <CLabel col>Company</CLabel>
                <CCol sm={8}>
                  <CInputGroup>
                    <CInput
                      value={store.itemCompanyName}
                      onChange={preventInputOnChange}
                    />
                    <CInputGroupAppend>
                      <CInputGroupText
                        tag="a"
                        href={item ? `#company-${item.company_id}` : '#'}
                      >
                        <CIcon name="cil-arrow-right" />
                      </CInputGroupText>
                    </CInputGroupAppend>
                  </CInputGroup>
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <GetLabelWithInfo info="Actual loan status" col>
                  Actual Loan Status
                </GetLabelWithInfo>
                <CCol sm={8}>
                  <CInput
                    value={store.item?.lms?.status}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <GetLabelWithInfo info="Loan Amount" col>
                  Principal Balance
                </GetLabelWithInfo>
                <CCol sm={8}>
                  <CInput
                    value={store.itemLoanPrincipalBalance}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <GetLabelWithInfo info="Remaining amount on the loan" col>
                  Loan Outstanding Amount
                </GetLabelWithInfo>
                <CCol sm={8}>
                  <CInput
                    value={store.formattedAmount(
                      store.itemLoanOutstandingAmount,
                    )}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <GetLabelWithInfo info="Payments in processing" col>
                  Processing Payment Amount
                </GetLabelWithInfo>
                <CCol sm={8}>
                  <CInput
                    value={store.formattedAmount(store.totalProcessingPayments)}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <GetLabelWithInfo info="Amount Entered by customer" col>
                  Requested Amount
                </GetLabelWithInfo>
                <CCol sm={8}>
                  <CInput
                    value={store.itemApprovedAmount}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <GetLabelWithInfo
                  info="Issued Amount (the loan without the fee)"
                  col
                >
                  Approved Amount
                </GetLabelWithInfo>
                <CCol sm={8}>
                  <CInput
                    value={store.itemApprovedAmount}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              {store.item?.invoiceDetails?.cardId && (
                <CFormGroup row>
                  <GetLabelWithInfo info="Amount used by customer on VC" col>
                    Used Amount
                  </GetLabelWithInfo>
                  <CCol sm={8}>
                    <CInput
                      value={store.formattedAmount(
                        store.item?.usedAmount || store.item?.approvedAmount,
                      )}
                      onChange={preventInputOnChange}
                      className="text-right"
                    />
                  </CCol>
                </CFormGroup>
              )}
              <CFormGroup row>
                <CLabel col>Loan Fee</CLabel>
                <CCol sm={8}>
                  <CInput
                    value={store.itemUnderwriting}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CLabel col>Late Amount</CLabel>
                <CCol sm={8}>
                  <CInput
                    value={store.formattedAmount(store.itemLateAmount)}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CLabel col>Next Payment Amount</CLabel>
                <CCol sm={8}>
                  <CInput
                    value={store.formattedAmount(
                      store.nextPaymentDisplayAmount,
                    )}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CLabel col>Number of Business Days Late</CLabel>
                <CCol sm={8}>
                  <CInput
                    value={store.itemNumberOfDaysLate}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CLabel col>Next Payment Date</CLabel>
                <CCol sm={8}>
                  <CInput
                    value={store.itemNextPaymentDate}
                    onChange={preventInputOnChange}
                    className={classNames('text-right', {
                      'text-danger':
                        store.lastInstallment?.status ===
                        LOAN_REPAYMENT_STATUS.LATE,
                    })}
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CLabel col>Refund Amount</CLabel>
                <CCol sm={8}>
                  <CInput
                    value={store.formattedAmount(store.itemRefundAmount)}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <LoanAvailableBalance id={id} />
              <CFormGroup row>
                <GetLabelWithInfo
                  info="Current total amount outstanding for the customer"
                  col
                >
                  Customer Loans Outstanding
                </GetLabelWithInfo>
                <CCol sm={8}>
                  <CInput
                    value={store.formattedAmount(
                      store.item?.company.credit?.balance,
                    )}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <GetLabelWithInfo info="Amount approved by Ops team" col>
                  Customer Approved Limit
                </GetLabelWithInfo>
                <CCol sm={8}>
                  <CInput
                    value={store.formattedAmount(
                      store.item?.company.credit?.limit,
                    )}
                    onChange={preventInputOnChange}
                    className="text-right"
                  />
                </CCol>
              </CFormGroup>
              {!!store.item?.lms?.loanParameters?.[0]
                ?.downPaymentPercentage && (
                <CFormGroup row>
                  <CLabel col>Down Payment Percentage</CLabel>
                  <CCol sm={8}>
                    <CInput
                      value={toPercentage(
                        store.item.lms.loanParameters[0].downPaymentPercentage,
                      )}
                      onChange={preventInputOnChange}
                      className="text-right"
                    />
                  </CCol>
                </CFormGroup>
              )}
              {!!store.item?.lms?.loanParameters?.[0]?.downPaymentAmount && (
                <CFormGroup row>
                  <CLabel col>Down Payment Amount</CLabel>
                  <CCol sm={8}>
                    <CInput
                      value={store.formattedAmount(
                        store.item.lms.loanParameters[0].downPaymentAmount,
                      )}
                      onChange={preventInputOnChange}
                      className="text-right"
                    />
                  </CCol>
                </CFormGroup>
              )}
              {(item?.metadata?.loanPackage?.finalPayment && (
                <CFormGroup row>
                  <CLabel col>Skip final payment</CLabel>
                  <CCol sm={8} className="my-auto">
                    <LoanSkipFinalPayment
                      type="switch"
                      id={store.item?._id || null}
                      item={store.item?.metadata || null}
                      onSuccess={(isSkipped: boolean) => {
                        store.setSkipFinalPayment(isSkipped)
                      }}
                    />
                  </CCol>
                </CFormGroup>
              )) ||
                ''}
            </CCol>
          </CRow>
        </CCardBody>
        {visible && currentUser && (
          <ChangeLoanStatusModal
            loanIdToChange={store.item?.lms?.id}
            onClose={onChangeLoanStatusModalClose}
            fetchDetails={fetchDetails}
            currentUser={currentUser}
            loanApplicationId={store.item?._id}
          />
        )}
      </CCard>
      {item && item.company.owners?.length > 0 ? (
        <CCard id={`company-${item.company_id}`}>
          <CCardHeader>
            <CCardTitle className="mb-0">Company owners</CCardTitle>
          </CCardHeader>
          <CCardBody>
            <CDataTable
              fields={['phone', 'email', 'firstName', 'lastName']}
              items={item.company.owners.slice()}
              border
              hover
              size="sm"
              striped
            />
          </CCardBody>
        </CCard>
      ) : null}
      {(item?.operations.length || 0) > 0 && <Transactions store={store} />}
      {(item?.lms?.loanReceivables.length || 0) > 0 && (
        <ReceivablesTable store={store} fetchDetails={fetchDetails} />
      )}
      <LmsPaymentsTable fetchDetails={fetchDetails} loading={loading} />
      <NotesTable />
    </div>
  )
})

interface BalanceState {
  availableBalance?: number
  error?: string
}

function LoanAvailableBalance(props: { id: string }) {
  const [load, setLoad] = useState(false)
  const [state, setState] = useState<BalanceState>({})

  const { id = '' } = props
  useEffect(() => {
    if (id && load) {
      const req = routes.admin.getLoanAvailableBalance(id)
      req
        .then((res) => {
          setState(res)
        })
        .catch((e) => {
          const error =
            e.response?.data?.message ||
            e.data?.message ||
            e.message ||
            [e.status, e.statusText].filter(Boolean).join(' ') ||
            e.toString()
          setState({ error })
        })
      return () => {
        req.cancel!()
      }
    }
    return () => {}
  }, [id, load])

  const { availableBalance, error } = state
  const value =
    availableBalance || availableBalance === 0
      ? numbro(availableBalance).formatCurrency({
          mantissa: 2,
          thousandSeparated: true,
        })
      : error || ''
  return (
    <CFormGroup row>
      <GetLabelWithInfo info="Connected Bank Balance (via Finicity)" col>
        Available Bank Balance
      </GetLabelWithInfo>
      <CCol sm={8}>
        <CInputGroup>
          <CInput value={value} readOnly />
          <CInputGroupAppend>
            <CButton
              color="warning"
              onClick={() => {
                setLoad(true)
              }}
              disabled={load}
            >
              Get balance
            </CButton>
          </CInputGroupAppend>
        </CInputGroup>
      </CCol>
    </CFormGroup>
  )
}

const transactionFields = ['date', 'type', 'status', 'amount', 'transactions']
const transactionScopedSlots = {
  date: (item) => (
    <td>{moment(item.date || item.createdAt).format('MM/DD/YYYY')}</td>
  ),
  amount: (item) => <AmountCell value={item.amount} />,
  transactions: (item) => <DetailTransactions item={item} />,
}

interface ITransactionsProps {
  store: ILoanStore
}

const Transactions: FC<ITransactionsProps> = observer(function Transactions(
  props,
) {
  const { item } = props.store
  const items = item?.operations.slice() || []
  return (
    <CCard>
      <CCardHeader>
        <CRow className="align-items-center">
          <CCol>
            <CCardTitle className="mb-0">Transactions</CCardTitle>
          </CCol>
        </CRow>
      </CCardHeader>
      <CCardBody>
        <CDataTable
          fields={transactionFields}
          items={items}
          scopedSlots={transactionScopedSlots}
          border
          hover
          responsive
          size="sm"
          striped
        />
      </CCardBody>
    </CCard>
  )
})

const DetailTransactions = observer(function DetailTransactions(props: {
  item: any
}) {
  const { transactions } = props.item
  return (
    <td>
      {transactions?.map((t) => (
        <div
          key={t.metadata.transactionNumber}
          className="pb-2 mb-2 border-bottom"
        >
          <CRow>
            <CCol>{t.metadata.accountNumber}</CCol>
            <CCol className="text-right">{t.payment_method}</CCol>
            <CCol className="text-right">{t.metadata.transactionNumber}</CCol>
          </CRow>
          <CRow>
            <CCol>{t.status}</CCol>
            <CCol className="text-monospace text-right">
              {numbro(t.fee).formatCurrency({
                mantissa: 2,
                thousandSeparated: true,
              })}
            </CCol>
            <CCol className="text-monospace text-right">
              {numbro(t.amount).formatCurrency({
                mantissa: 2,
                thousandSeparated: true,
              })}
            </CCol>
          </CRow>
        </div>
      ))}
    </td>
  )
})
