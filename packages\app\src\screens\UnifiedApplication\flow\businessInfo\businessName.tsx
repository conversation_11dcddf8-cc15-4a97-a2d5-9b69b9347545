import React, { FC } from 'react'
import { BtInput } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { Spacer } from '../../../../ui/atoms'
import { WizardStepDescription } from '../../components/ApplicationWizard/WizardStepDescription'

const BusinessName: FC = observer(() => {
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  const handleLegalNameChange = (legalName: string) => {
    store.document.data.businessInfo.businessName = {
      ...(store.document.data.businessInfo.businessName ?? {}),
      legalName,
    }
  }

  const handleDbaChange = (dba: string) => {
    store.document.data.businessInfo.businessName = {
      ...(store.document.data.businessInfo.businessName ?? {}),
      dba,
    }
  }

  return (
    <>
      <Spacer height={store.isInReview ? 24 : 4} />

      <BtInput
        size="large"
        testID="BusinessNameInput"
        value={store.document.data.businessInfo.businessName?.legalName || ''}
        label={t('Business.NameLabel')}
        onChangeText={handleLegalNameChange}
      />

      <Spacer height={store.isInReview ? 24 : 4} />

      {!store.isInReview && (
        <WizardStepDescription
          description={{
            text: 'Business.DbaTitle',
            style: { marginTop: 24 },
          }}
        />
      )}

      <BtInput
        size="large"
        testID="DbaInput"
        value={store.document.data.businessInfo.businessName?.dba || ''}
        label={t('Business.DbaLabel')}
        onChangeText={handleDbaChange}
      />
    </>
  )
})

export default {
  component: BusinessName,
  title: {
    text: 'Business.Name',
  },
  description: {
    text: 'Business.NameDescription',
  },
  showFooterMessage: true,
}
