import { EInvoiceType, exceptions, InvoicePaymentType } from '@linqpal/models'
import moment, { Moment } from 'moment-timezone'
import {
  AzureService,
  Company,
  CustomerAccount,
  LedgerService,
  LMS,
  LoanPaymentPlan,
  LoanPricingPackage,
  Operation,
} from '../../..'
import { ICompany, IInvoice, ILoanPaymentPlan } from '../../models/types'
import { Logger } from '../logger/logger.service'
import {
  CREATE_FACTORING_DISBURSEMENT,
  CREATE_FACTORING_FINAL_PAYMENT,
  EFeeType,
  EPaymentMethod,
  EPaymentType,
  IPayload,
} from '../payment/types'
import {
  AdvancePaymentDay,
  ArAdvanceStatus,
  PaymentDelayCodeParser,
} from '@linqpal/models/src/dictionaries/factoring'
import mongoose, { ClientSession } from 'mongoose'
import {
  ICreateCredit,
  ICustomerSettings,
} from '@linqpal/models/src/types/routes'
import { PricingProduct } from '@linqpal/models/src/dictionaries/pricingProduct'
import { onBoardingService } from '../onBoarding/onBoarding.service'
import {
  IArAdvanceCreditInfo,
  ICreditApplication,
} from '@linqpal/models/src/interfaces'
import { CreditApplicationType } from '@linqpal/models/src/dictionaries/creditApplicationType'
import {
  InHouseCreditStatus,
  InHouseCreditStatusType,
  invoiceSchemaStatus,
  invoiceStatus,
  LmsCreditStatus,
  OPERATION_STATUS,
  OPERATION_TYPES,
} from '@linqpal/models/src/dictionaries'
import { CompanyUtils } from '@linqpal/models/src/helpers/companyUtils'
import {
  ICredit,
  LoanOrigin,
  ReceivableStatus,
  ReceivableType,
  ScheduleStatus,
} from '../lms.service'
import sum from 'lodash/sum'
import {
  DrawApprovalStatus,
  IDrawApproval,
  PayableType,
} from '../onBoarding/types'
import { IDotNetInvoice, invoiceService } from '../invoice.dotnet.service'
import { CriticalError } from '@linqpal/models/src/types/exceptions'
import { promiseSettledAggregate } from '@linqpal/models/src/helpers'
import { DotNetMapper } from '../../helpers/DotNetMapper'
import {
  CreditApplicationStatus,
  ExcludeApprovedCreditApplicationStatus,
} from '@linqpal/models/src/dictionaries/creditApplicationStatus'
import { compatibilityService } from '../compatibility/compatibility.service'
import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'
import { Invoice } from '../../models'
import { findBuilderByAccountId } from '../invoices.service/findBuilderAccount'
import { SystemUsers } from '@linqpal/models/src/dictionaries/systemUsers'
import { FundingSources } from '@linqpal/models/src/dictionaries/fundingSources'

export class FactoringService {
  private static logger = new Logger({ module: 'FactoringService' })

  public static async tryStartArAdvance(
    receivable: IInvoice,
    supplier: ICompany,
    originalDueDate: Date | undefined,
    session: ClientSession | null,
  ): Promise<boolean> {
    const {
      isValid,
      paymentPlan,
      pricingPackageId,
      cardPricingPackageId,
      loanPlanId,
    } = await this.ensureArAdvanceOptions(supplier, receivable)

    if (!isValid || !paymentPlan) return false

    const paymentDelay = PaymentDelayCodeParser.getPaymentDelay(
      paymentPlan.paymentDelayCode,
    )

    receivable.invoice_due_date = paymentDelay.moment.format()

    receivable.paymentDetails = {
      paymentType: InvoicePaymentType.FACTORING,
      pricingPackageId,
      cardPricingPackageId,
      customerFee: null,
      supplierFee: null,
      loanPlanId,
      originalDueDate:
        receivable.type === EInvoiceType.INVOICE
          ? originalDueDate ?? paymentDelay.moment.toDate()
          : null,
      arAdvanceStatus: ArAdvanceStatus.InReview,
      fees: null,
    }

    if (receivable.type === EInvoiceType.QUOTE) {
      receivable.status = invoiceSchemaStatus.authorizationInReview
    }

    await receivable.save({ session })
    try {
      await compatibilityService.runDotNetDEForInHouseInvoice(receivable.id)
    } catch (e) {
      console.log('Error happened during Invoice Approval DE running', e)
    }
    return true
  }

  public static async ensureArAdvanceOptions(
    supplier: ICompany | null,
    invoice: Pick<
      IInvoice,
      'id' | 'type' | 'customer_account_id' | 'total_amount' | 'status'
    >,
  ): Promise<{
    isValid: boolean
    paymentPlan: ILoanPaymentPlan | null
    pricingPackageId: string | null
    cardPricingPackageId: string | null
    loanPlanId: string | null
  }> {
    const invalidResponse = {
      isValid: false,
      paymentPlan: null,
      pricingPackageId: null,
      cardPricingPackageId: null,
      loanPlanId: null,
    }

    if (![EInvoiceType.INVOICE, EInvoiceType.QUOTE].includes(invoice.type))
      return invalidResponse

    if (invoice.total_amount <= 0 && invoice.status !== invoiceStatus.draft) {
      // prettier-ignore
      this.logger.info(`AR Advance is not applicable for non-positive amount ${invoice.total_amount}`)
      return invalidResponse
    }

    const customerAccount = await CustomerAccount.findById(
      invoice.customer_account_id,
    )

    if (!supplier) {
      this.logger.warn(`no supplier for invoice ${invoice.id}`)
      return invalidResponse
    }

    if (!customerAccount) {
      this.logger.warn(`no customer for invoice ${invoice.id}`)
      return invalidResponse
    }

    if (!supplier.settings?.arAdvance?.isEnabled) {
      // prettier-ignore
      this.logger.info(`AR Advance is disabled for supplier ${supplier.id}`)
      return invalidResponse
    }

    if (!customerAccount.settings?.inHouseCredit?.isEnabled) {
      this.logger.info(
        `In-House Credit is disabled for customer account ${customerAccount.id}`,
      )
      return invalidResponse
    }

    if (customerAccount.type !== CustomerAccountType.IHC) {
      // prettier-ignore
      this.logger.info(`In-House Credit is disabled for trade credit customers`)
      return invalidResponse
    }

    if (customerAccount.isDeleted) {
      // prettier-ignore
      this.logger.info(
        `In-House Credit is disabled for deleted customer account`,
      )
      return invalidResponse
    }

    const loanPlanId = customerAccount.settings?.inHouseCredit?.factoringTerm
    const pricingPackageId =
      customerAccount.settings?.inHouseCredit?.supplierPackage

    if (!loanPlanId) {
      // prettier-ignore
      this.logger.warn(
        `no factoring term selected for customer account ${customerAccount.id}`,
      )
      return invalidResponse
    }

    if (!pricingPackageId) {
      // prettier-ignore
      this.logger.warn(
        `no supplier package selected for customer account ${customerAccount.id}`,
      )
      return invalidResponse
    }

    const paymentPlan = await LoanPaymentPlan.findById(loanPlanId)

    if (!paymentPlan?.paymentDelayCode) {
      // prettier-ignore
      this.logger.warn(`no delay code found for plan ${loanPlanId}`)
      return invalidResponse
    }

    const cardPricingPackageId = supplier.settings.cardPricingPackageId ?? null

    return {
      isValid: true,
      paymentPlan,
      pricingPackageId,
      cardPricingPackageId,
      loanPlanId,
    }
  }

  public static async getUpdatedPaymentDetails(
    company: ICompany,
    invoice: Pick<
      IInvoice,
      'id' | 'type' | 'customer_account_id' | 'total_amount' | 'status'
    >,
  ) {
    const defaultPaymentDetails = {
      paymentType: null,
      pricingPackageId: null,
      cardPricingPackageId: null,
      loanPlanId: null,
      originalDueDate: null,
      arAdvanceStatus: ArAdvanceStatus.NotApplied,
    }
    if (!invoice.customer_account_id) {
      return defaultPaymentDetails
    }

    const { isValid, paymentPlan } = await this.ensureArAdvanceOptions(
      company,
      invoice,
    )

    return isValid && paymentPlan
      ? { ...defaultPaymentDetails, paymentType: InvoicePaymentType.FACTORING }
      : defaultPaymentDetails
  }

  public static async issueLoan(drawApproval: IDrawApproval) {
    const payables = drawApproval?.payables?.filter(
      (p) => p.type === PayableType.Invoice,
    )

    if (!payables.length) {
      throw new CriticalError(`Draw approval doesn't have any payables`, {
        drawApproval,
      })
    }

    const invoice = await invoiceService.getInvoice(payables[0].id)

    if (!invoice)
      throw new CriticalError(
        `no invoice ${payables[0].id} found for draw approval`,
        { drawApproval },
      )

    if (!invoice.customer_account_id)
      throw new CriticalError(
        `Customer account id is null in Draw Approval's invoice`,
        { invoice, drawApproval },
      )

    if (invoice.paymentDetails?.paymentType !== InvoicePaymentType.FACTORING) {
      this.logger.info(
        { invoice, drawApproval },
        `Unable to create a loan and disburse advance payment for non-factoring invoice`,
      )
      return
    }

    const loanPlanId = invoice.paymentDetails?.loanPlanId

    const loanPlan = await LoanPaymentPlan.findById(loanPlanId)

    if (!loanPlan)
      throw new CriticalError(
        `Loan Payment plan with ${loanPlanId} is not found`,
        { invoice, drawApproval },
      )

    const {
      id: loan_id,
      amount,
      fee,
    } = await LMS.createLoan(
      drawApproval.companyId ?? '',
      drawApproval.companyName,
      loanPlan.lmsTemplateId,
      drawApproval.drawAmount,
      drawApproval.einHash ?? '',
      LoanOrigin.Factoring,
      drawApproval.debtInvestor ?? FundingSources.Arcadia,
      drawApproval.projectId,
      drawApproval.merchantId,
      drawApproval.merchantName,
      drawApproval.id,
      drawApproval.payables
        .filter((item) => item.type === PayableType.Invoice)
        .map((item) => ({
          ...item,
          payableId: item.id,
        })),
    )

    await LedgerService.handleLoanIssue({
      loanId: loan_id,
      customerId: invoice.customer_account_id,
      amount: amount,
      fee: fee,
    })

    this.logger.info(
      { loan_id, amount, fee },
      'Loan successfully created, disbursing advance payment',
    )

    await this.disburseAdvancePayment(drawApproval, invoice)
    await LMS.activateLoan(loan_id)

    this.logger.info(`Loan with id ${loan_id} is activated`)
  }

  private static async disburseAdvancePayment(
    drawApproval: IDrawApproval,
    invoice: IDotNetInvoice,
  ) {
    const advancePayment = await Operation.findOne({
      owner_id: invoice.id,
      type: OPERATION_TYPES.INVOICE.FACTORING_DISBURSEMENT,
      status: mongoose.trusted({
        $in: [OPERATION_STATUS.SUCCESS, OPERATION_STATUS.PROCESSING],
      }),
    })

    if (advancePayment) {
      this.logger.warn(
        { advancePayment: advancePayment.toObject() },
        `Advance payment exists for invoice ${invoice.id} `,
      )
      return
    }

    const [supplier, customerCompany, pricingPackage] =
      await promiseSettledAggregate([
        Company.findById(invoice.company_id),
        findBuilderByAccountId(invoice.customer_account_id!),
        LoanPricingPackage.findById(invoice.paymentDetails?.pricingPackageId),
      ])

    // prettier-ignore
    if (!pricingPackage) throw new Error(`No pricing package for invoice ${invoice.id}`)
    if (!supplier) throw new Error(`No supplier for invoice ${invoice.id}`)

    const { advanceRate, advancePaymentDay } = pricingPackage.metadata

    const paymentAmount = (drawApproval.drawAmount * advanceRate) / 100

    let paymentDate: Moment

    if (advancePaymentDay === AdvancePaymentDay.TD0) {
      paymentDate = moment().utc()
    } else if (process.env.LP_MODE === 'prod') {
      paymentDate = PaymentDelayCodeParser.getPaymentDelay(
        advancePaymentDay,
        moment(drawApproval.approvedAt),
      ).moment.utc()
    } else {
      paymentDate = PaymentDelayCodeParser.getTestPaymentDelay(
        advancePaymentDay,
        moment(drawApproval.approvedAt),
      ).moment.utc()
    }

    await this.sendAdvancePaymentEvent(
      paymentAmount,
      paymentDate,
      invoice,
      supplier,
      customerCompany,
    )
  }

  public static async disburseFinalPayment(invoice: IInvoice) {
    // https://linqpal.atlassian.net/wiki/spaces/BNPL/pages/1910276097/Factoring+Design#Process-of-final-payment-to-merchant
    // https://linqpal.atlassian.net/wiki/spaces/BNPL/pages/1910276097/Factoring+Design#Merchant-Fee-Computation-and-Final-Payment-Amount

    if (invoice.paymentDetails?.paymentType !== InvoicePaymentType.FACTORING) {
      // prettier-ignore
      this.logger.info(`unable to disburse final payment for non-factoring invoice ${invoice.id}`)
      return
    }

    const advancePayment = await Operation.findOne({
      owner_id: invoice.id,
      type: OPERATION_TYPES.INVOICE.FACTORING_DISBURSEMENT,
      status: OPERATION_STATUS.SUCCESS,
    })

    if (!advancePayment) {
      // prettier-ignore
      this.logger.info(`No successful advance payment for invoice ${invoice.id}, final payment delayed`)
      return
    }

    const pricingPackageId = invoice.paymentDetails?.pricingPackageId

    if (!pricingPackageId)
      throw new Error(`no pricing package for invoice ${invoice.id}`)

    // prettier-ignore
    this.logger.info({ advancePayment }, 'advance payment is successful, sending final payment')

    const [supplier, customerCompany, pricingPackage] = await Promise.all([
      Company.findById(invoice.company_id),
      findBuilderByAccountId(invoice.customer_account_id!),
      LoanPricingPackage.findById(pricingPackageId),
    ])

    // prettier-ignore
    if (!pricingPackage) throw new Error(`no pricing package ${pricingPackageId} found for invoice ${invoice.id}`)
    if (!supplier) throw new Error(`no supplier for invoice ${invoice.id}`)

    const invoiceFees = await this.calculateSupplierFees(invoice, supplier)

    const merchantFeePercentage = pricingPackage.metadata?.merchant ?? 0

    const merchantFee = (invoice.total_amount * merchantFeePercentage) / 100
    const totalFee = merchantFee + invoiceFees

    const finalAmount = invoice.total_amount - advancePayment.amount - totalFee

    this.logger.info(
      { finalAmount, merchantFee, lateAndPenaltyFee: invoiceFees, totalFee },
      'calculated payment details',
    )

    await this.sendFinalPaymentEvent(
      finalAmount,
      totalFee,
      invoice,
      supplier,
      customerCompany,
    )
  }

  public static async getArAdvanceCreditInfo(
    merchantId: string,
  ): Promise<IArAdvanceCreditInfo> {
    if (!merchantId) throw new Error('companyId is required')

    //TODO: VK: use LMS.findCredits when ready to filter records at LMS side

    const [companyCredits, quoteApprovals] = await Promise.all([
      LMS.getCreditCompanyInfo(merchantId),
      onBoardingService.findDrawApprovals({
        merchantId: merchantId,
        status: DrawApprovalStatus.Approved,
        payableTypes: [PayableType.Quote],
      }),
    ])

    const totalQuotesAuthorized = sum(
      quoteApprovals.result?.map((approval) => approval.drawAmount) ?? [],
    )

    if (!companyCredits?.length) {
      return {
        limit: 0,
        availableCredit: 0,
        principalBalance: 0,
        outstandingCredit: 0,
        totalQuotesAuthorized,
      }
    }

    const arAdvanceCredit = companyCredits.find(
      (credit) => credit.product === PricingProduct.ArAdvance,
    )

    return {
      limit: arAdvanceCredit?.creditLimit ?? 0,
      availableCredit: arAdvanceCredit?.creditDetails?.availableCredit ?? 0,
      principalBalance: arAdvanceCredit?.creditDetails?.principalBalance ?? 0,
      outstandingCredit: arAdvanceCredit?.creditDetails?.outstandingCredit ?? 0,
      totalQuotesAuthorized,
    }
  }

  public static async createArAdvanceCompanyCredit(
    userId: string,
    credit: ICreateCredit,
  ) {
    let { companyId, creditApplicationId, creditLimit } = credit
    if (!companyId) {
      throw new exceptions.LogicalError('Company id was not provided.')
    }
    const company = await Company.findById(companyId)
    if (!company) {
      throw new exceptions.LogicalError('Company was not found.')
    }

    if (
      !company?.settings?.arAdvance?.isEnabled ||
      !company?.settings?.arAdvance?.merchantLimit
    ) {
      throw new exceptions.LogicalError(
        'AR advance is not enabled for the company.',
      )
    }

    if (!creditLimit) {
      creditLimit = company.settings.arAdvance.merchantLimit
    }

    if (!creditApplicationId) {
      const approvedApplication = await onBoardingService.getCreditApplications(
        {
          companyId,
          type: CreditApplicationType.LineOfCredit,
        },
      )
      if (!approvedApplication?.length) {
        throw new exceptions.LogicalError('Company has no credit application.')
      }
      creditApplicationId = approvedApplication[0].id
    }

    const companyCredits = await LMS.getCreditCompanyInfo(companyId)

    if (companyCredits?.length) {
      const arAdvanceCredit = companyCredits.find(
        (elem) => elem.product === PricingProduct.ArAdvance,
      )
      if (arAdvanceCredit) {
        const updatedArAdvance = await LMS.updateCreditDetails(
          arAdvanceCredit.id,
          userId,
          {
            creditLimit,
          },
        )
        return { id: updatedArAdvance?.id }
      }
    }

    const createdArAdvanceCredit = await LMS.createCredit({
      companyId,
      creditApplicationId,
      merchantId: companyId,
      startDate: new Date().toISOString().substring(0, 10),
      currency: 'usd',
      creditLimit,
      productType: PricingProduct.ArAdvance,
      revenueFallPercentage: 0,
    })

    return { id: createdArAdvanceCredit?.id }
  }

  public static calculateStatus(
    application: ICreditApplication | null,
    settings: ICustomerSettings,
    credit?: ICredit,
  ) {
    if (!application) {
      return { status: InHouseCreditStatus.NotApplied }
    }

    const applicationStatusMap: Record<
      ExcludeApprovedCreditApplicationStatus,
      InHouseCreditStatusType
    > = {
      [CreditApplicationStatus.New]: InHouseCreditStatus.IncompleteApplication,
      [CreditApplicationStatus.SentBack]:
        InHouseCreditStatus.IncompleteApplication,
      [CreditApplicationStatus.Processing]:
        InHouseCreditStatus.IncompleteApplication,
      [CreditApplicationStatus.ExecutionFailed]:
        InHouseCreditStatus.IncompleteApplication,
      [CreditApplicationStatus.Processed]: InHouseCreditStatus.PendingReview,
      [CreditApplicationStatus.Review]: InHouseCreditStatus.PendingReview,
      [CreditApplicationStatus.Rejected]: InHouseCreditStatus.Rejected,
      [CreditApplicationStatus.Canceled]: InHouseCreditStatus.Cancelled,
    }

    const creditStatusMap: Record<LmsCreditStatus, InHouseCreditStatusType> = {
      [LmsCreditStatus.Active]: InHouseCreditStatus.GoodStanding,
      [LmsCreditStatus.PastDue]: InHouseCreditStatus.PastDue,
      [LmsCreditStatus.OnHold]: InHouseCreditStatus.AccountOnHold,
      [LmsCreditStatus.InCollection]: InHouseCreditStatus.InCollection,
      [LmsCreditStatus.Closed]: InHouseCreditStatus.Closed,
    }

    const applicationStatus =
      applicationStatusMap[
        application.status as ExcludeApprovedCreditApplicationStatus
      ]

    if (applicationStatus) {
      return { status: applicationStatus }
    }

    if (!settings.inHouseCredit?.isEnabled) {
      return { status: InHouseCreditStatus.Inactive }
    }

    if (credit && credit.status) {
      const creditStatus = creditStatusMap[credit.status as LmsCreditStatus]
      if (creditStatus) {
        return { status: creditStatus }
      }
    }

    return { status: InHouseCreditStatus.GoodStanding }
  }

  public static async cancelFactoringForInvoicesIfAny(
    invoices: IInvoice[],
    session: mongoose.ClientSession,
  ) {
    await this.processInvoicesToCancelFactoring(invoices)

    await this.processInvoicesToChangePaymentType(invoices, session)
  }

  private static async processInvoicesToCancelFactoring(invoices: IInvoice[]) {
    const invoicesToCancelFactoring = invoices.filter(
      (invoice) =>
        invoice.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
        invoice.paymentDetails.arAdvanceStatus === ArAdvanceStatus.InReview,
    )

    if (invoicesToCancelFactoring.length > 0) {
      this.logger.info(
        {
          invoices: invoicesToCancelFactoring.map((invoice) =>
            invoice.toJSON(),
          ),
        },
        'Cancelling In-House Invoice for these invoices',
      )

      const drawApprovalsResponse =
        await onBoardingService.getIHCDrawApprovalsByInvoiceIds(
          invoicesToCancelFactoring.map((invoice) => invoice.id),
        )

      const { result: drawApprovals } = drawApprovalsResponse

      this.logger.info({ drawApprovalsResponse }, 'Patching draw approvals')

      const patchPromises = drawApprovals?.map((drawApproval: any) =>
        onBoardingService.patchDrawApproval(drawApproval.id, {
          status: DrawApprovalStatus.Canceled,
          updatedBy: 'AutomaticNodeJsServices',
        }),
      )

      await promiseSettledAggregate(patchPromises)
    }
  }

  private static async processInvoicesToChangePaymentType(
    invoices: IInvoice[],
    session: mongoose.ClientSession,
  ) {
    const invoicesToSetPaymentMethod = invoices.filter(
      (invoice) =>
        invoice.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
        [ArAdvanceStatus.Placed, ArAdvanceStatus.InReview].includes(
          invoice.paymentDetails.arAdvanceStatus,
        ),
    )

    if (invoicesToSetPaymentMethod.length > 0) {
      this.logger.info(
        {
          invoices: invoicesToSetPaymentMethod.map((invoice) =>
            invoice.toJSON(),
          ),
        },
        "Updating 'paymentType' and 'arAdvanceStatus' for these invoices",
      )

      await Invoice.updateMany(
        {
          _id: mongoose.trusted({
            $in: invoicesToSetPaymentMethod.map((invoice) => invoice._id),
          }),
        },
        {
          $set: {
            'paymentDetails.paymentType': InvoicePaymentType.PAYNOW,
            'paymentDetails.arAdvanceStatus': ArAdvanceStatus.NotApplied,
          },
        },
      ).session(session)
    }
  }

  private static async sendFinalPaymentEvent(
    amount: number,
    fee: number,
    invoice: IInvoice,
    supplier: ICompany,
    customerCompany: ICompany | null,
  ) {
    const payload: IPayload = {
      flowTemplateCode: CREATE_FACTORING_FINAL_PAYMENT,
      blueTapeCorrelationId: `${invoice.id}-final-payment`,
      createdBy: SystemUsers.AutomaticNodeJsService,
      details: {
        date: moment().format(),
        currency: 'USD',
        requestedAmount: amount,
        paymentMethod: EPaymentMethod.ach,
        customerDetails: {
          id: customerCompany?.id,
          name: CompanyUtils.getCompanyName(customerCompany),
          accountId: '',
        },
        sellerDetails: {
          companyId: supplier.id,
          name: CompanyUtils.getCompanyName(supplier),
          paymentSettings: {
            merchantAchDelayDays: 0,
          },
        },
        payablesDetails: [
          {
            id: invoice.id,
            payableType: EPaymentType[invoice.type],
            payableAmount: invoice.total_amount,
            requestedAmount: amount,
            discountAmount: 0,
          },
        ],
        feeDetails: [
          {
            amount: fee,
            companyId: supplier.id,
            description: 'Merchant fee',
            type: EFeeType.merchantFee,
          },
        ],
      },
    }

    await AzureService.sendServiceBusMessage(
      process.env.AZ_PAYMENT_SERVICE_QUEUE_CONNECTION_STRING ?? '',
      process.env.AZ_PAYMENT_SERVICE_QUEUE_NAME ?? '',
      payload,
    )
  }

  private static async sendAdvancePaymentEvent(
    amount: number,
    paymentDate: Moment,
    invoice: IDotNetInvoice,
    supplier: ICompany,
    customerCompany: ICompany | null,
  ) {
    const payload: IPayload = {
      flowTemplateCode: CREATE_FACTORING_DISBURSEMENT,
      blueTapeCorrelationId: `${invoice.id}-advance-payment`,
      createdBy: SystemUsers.AutomaticNodeJsService,
      details: {
        date: moment().format(),
        currency: 'USD',
        requestedAmount: amount,
        paymentMethod: EPaymentMethod.ach,
        executeAfter: paymentDate.format(),
        customerDetails: {
          id: customerCompany?.id,
          name: CompanyUtils.getCompanyName(customerCompany),
          accountId: '',
        },
        sellerDetails: {
          companyId: supplier.id,
          name: CompanyUtils.getCompanyName(supplier),
          paymentSettings: {
            merchantAchDelayDays: 0,
          },
        },
        payablesDetails: [
          {
            id: invoice.id,
            payableType: DotNetMapper.invoiceTypeToEPaymentType(invoice.type),
            payableAmount: invoice.total_amount,
            requestedAmount: amount,
            discountAmount: 0,
          },
        ],
      },
    }

    await AzureService.sendServiceBusMessage(
      process.env.AZ_PAYMENT_SERVICE_QUEUE_CONNECTION_STRING ?? '',
      process.env.AZ_PAYMENT_SERVICE_QUEUE_NAME ?? '',
      payload,
    )

    this.logger.info({ payload }, 'CREATE_FACTORING_DISBURSEMENT is sent')
  }

  static async calculateCustomerFees(invoice: IInvoice, supplier: ICompany) {
    // prettier-ignore
    const receivableTypes = !supplier.settings?.arAdvance?.isLateInterestChargedToMerchant
      ? [
          ReceivableType.LatePaymentFee,
          ReceivableType.ManualLatePaymentFee,
          ReceivableType.PenaltyInterestFee,
        ]
      : []

    const customerFee = invoice.paymentDetails?.customerFee ?? 0
    const lateFees = await this.calculateInvoiceFees(invoice, receivableTypes)

    return lateFees + customerFee
  }

  static async calculateSupplierFees(invoice: IInvoice, supplier: ICompany) {
    if (!supplier.settings?.arAdvance?.isLateInterestChargedToMerchant) return 0

    return this.calculateInvoiceFees(invoice, [
      ReceivableType.LatePaymentFee,
      ReceivableType.ManualLatePaymentFee,
      ReceivableType.PenaltyInterestFee,
    ])
  }

  static async calculateInvoiceUnpaidAmount(invoice: IInvoice) {
    const loan = (
      await LMS.findLoans({
        payableId: invoice.id,
        detailed: true,
      })
    )?.[0]

    if (!loan) {
      this.logger.warn(`no loan found for invoice ${invoice.id}`)
      return invoice.total_amount
    }

    const totalLoanAmount = loan.loanDetails?.totalLoanAmount ?? 0
    const totalFeesAmount = loan.loanDetails?.totalFeesAmount ?? 0
    const totalPaid = loan.loanDetails?.totalPaid ?? 0
    const totalProcessingPaymentsAmount =
      loan.loanDetails?.totalProcessingPaymentsAmount ?? 0

    const unpaidAmount =
      totalLoanAmount -
      totalFeesAmount -
      totalPaid -
      totalProcessingPaymentsAmount

    this.logger.info({ unpaidAmount }, 'calculated unpaid amount')

    return unpaidAmount
  }

  private static async calculateInvoiceFees(
    invoice: IInvoice,
    receivableTypes: ReceivableType[],
  ) {
    const loan = (
      await LMS.findLoans({
        payableId: invoice.id,
        detailed: true,
      })
    )?.[0]

    if (!loan) {
      this.logger.warn(`no loan found for invoice ${invoice.id}`)
      return 0
    }

    const fees =
      loan?.loanReceivables?.filter(
        (r) =>
          r.scheduleStatus === ScheduleStatus.Current &&
          r.status !== ReceivableStatus.Canceled &&
          receivableTypes.includes(r.type),
      ) ?? []

    const totalFee = sum(fees.map((f) => f.expectedAmount + f.adjustAmount))

    this.logger.info({ fees, totalFee }, 'excluding fees from final payment')

    return totalFee
  }
}
