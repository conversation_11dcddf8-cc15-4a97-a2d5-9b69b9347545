import {
  IGetApplicationRequest,
  IGetApplicationResponse,
} from '@linqpal/models/src/routes2/application/types'
import { TCompoundRoute } from '@linqpal/models/src/routes2/types'
import { authMiddleware } from '../middlewares'

export const getDraft: TCompoundRoute<
  IGetApplicationRequest,
  IGetApplicationResponse
> = {
  middlewares: {
    pre: [...authMiddleware.pre],
    post: [],
  },
  get: async (
    data: IGetApplicationRequest,
    req: IGetApplicationResponse,
  ): Promise<IGetApplicationResponse> => {
    console.log(data, req)
    return { draft: null }
  },
}
