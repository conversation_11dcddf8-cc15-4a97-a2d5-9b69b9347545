import { INormalizedApplicationDraft, IOwnerInfo } from '../mst'
import { annualRevenueMap, debtMap } from '../dictionaries'
import { OwnerTypes } from '../dictionaries/UnifiedApplication'

// wrapper around draft object to unify access to legacy draft schema
// and new draft schema for unified application
export function readDraft(
  draft: any | null,
): INormalizedApplicationDraft | null {
  if (!draft) return null

  return {
    normalized: draft.normalized,

    businessInfo_businessName:
      typeof draft.businessInfo_businessName === 'object'
        ? draft.businessInfo_businessName
        : {
            legalName:
              draft.businessInfo_businessName || draft.businessInfo_legalName,
          },

    businessInfo_businessPhone: draft.businessInfo_businessPhone,
    businessInfo_businessAddress: draft.businessInfo_businessAddress,
    businessInfo_ein: draft.businessInfo_ein,
    businessInfo_startDate:
      draft.businessInfo_startDate || draft.businessInfo_yearStarted,

    businessOwner_id: draft.businessOwner_id,
    businessOwner_ssn: draft.businessOwner_ssn || draft.personalInfo_ssn,
    businessOwner_address:
      draft.businessOwner_address || draft.personalInfo_address,
    businessOwner_birthdate:
      draft.businessOwner_birthdate || draft.personalInfo_birthday,
    businessOwner_firstName:
      draft.businessOwner_firstName || draft.personalInfo_firstName,
    businessOwner_lastName:
      draft.businessOwner_lastName || draft.personalInfo_lastName,
    businessOwner_phone:
      draft.businessOwner_phone || draft.personalInfo_phone || '',
    businessOwner_email:
      draft.businessOwner_email || draft.personalInfo_email || '',

    owners: draft.businessOwner_ownerName // unified app 1.0
      ? [
          {
            id: draft.businessOwner_id,
            type: OwnerTypes.INDIVIDUAL,
            lastName: draft.businessOwner_ownerName.lastname,
            firstName: draft.businessOwner_ownerName.firstname,
            phone: draft.businessOwner_ownerPhone,
            email: draft.businessOwner_ownerEmail,
            ssn: draft.businessOwner_ownerSSN,
            address: draft.businessOwner_ownerAddress?.address,
            city: draft.businessOwner_ownerAddress?.city,
            state: draft.businessOwner_ownerAddress?.state,
            zip: draft.businessOwner_ownerAddress?.zip,
            birthday: draft.businessOwner_ownerBirthdate,
            entityName: '',
            ein: '',
          },
        ]
      : getCoOwners(draft), // unified app 2.0 & pre-unified

    finance_debt:
      draft.finance_debt ||
      debtMap[draft.businessFinance_debt]?.toString() ||
      draft.businessFinance_debt?.toString(),
    finance_revenue:
      draft.finance_revenue ||
      annualRevenueMap[draft.businessFinance_annualRevenue]?.toString() ||
      draft.businessFinance_annualRevenue?.toString(),

    bank_details: draft.bank_details || draft.bankInformation_bankDetails,

    data: draft.data,
  }
}

function getCoOwners(draft: any): IOwnerInfo[] {
  const coOwners = Object.keys(draft)
    .filter((key) => /^coOwnerInfo_coOwner\d$/.test(key))
    .map((key) => draft[key])

  return coOwners
}
