// Types for the new flow structure

type FlowStep<TDocument = any, TOptions = any> =
  | '' // using an empty string as the less distracting placeholder for a step without options, can be null or an empty object
  | FlowStepOptions
  | ((document?: TDocument, options?: TOptions) => string)

interface FlowStepOptions {
  canSkip?: boolean
  skipPath?: string
}

interface FlowGroup<TDocument = any, TOptions = any> {
  [key: string]: FlowStep<TDocument, TOptions>
}

export interface Flow<TDocument = any, TOptions = any> {
  [key: string]: FlowGroup<TDocument, TOptions>
}

export class FlowController_v1<TDocument = any, TOptions = any> {
  private readonly _flow: Flow<TDocument, TOptions>

  constructor(flow: Flow<TDocument, TOptions>) {
    this._flow = flow
  }

  getFlowSteps(
    startStep?: string,
    includeReview = false,
    document?: TDocument,
    options?: TOptions,
  ): string[] {
    const doc = document || ({} as TDocument)
    const opts = options || ({} as TOptions)
    const steps: string[] = []

    let step = startStep || this.getFirstStep(doc, opts)

    while (step && (includeReview || !step.includes('review'))) {
      steps.push(step)
      step = this.findNextStep(step, doc, opts)
    }

    return steps
  }

  getGroupSteps(
    groupName: string,
    document?: TDocument,
    options?: TOptions,
  ): string[] {
    const doc = document || ({} as TDocument)
    const opts = options || ({} as TOptions)
    let step = this.getFirstGroupStep(groupName, doc, opts)
    const steps: string[] = []

    while (step && step.startsWith(`${groupName}.`)) {
      steps.push(step)
      step = this.findNextStep(step, doc, opts)
    }

    return steps
  }

  // TODO: VK: Review - first step con be conditional
  getFirstStep(document: TDocument, options: TOptions): string {
    const firstGroup = Object.keys(this._flow)[0]
    return this.getFirstGroupStep(firstGroup, document, options)
  }

  getFirstGroupStep(
    groupName: string,
    document: TDocument,
    options: TOptions,
  ): string {
    const firstStepName = Object.keys(this._flow[groupName])[0]
    return this.tryHandleConditionalJumps(
      groupName,
      firstStepName,
      document,
      options,
    )
  }

  findNextStep(path: string, document: TDocument, options: TOptions): string {
    const [currentGroupName, currentStepName] = path.split('.')

    const currentGroup = this._flow[currentGroupName]
    const stepNames = Object.keys(currentGroup)
    const stepIndex = stepNames.indexOf(currentStepName)

    let targetGroupName = currentGroupName
    let targetStepName: string | undefined

    // If current group has more steps, go to next one,
    // otherwise go to the first step of the next group
    if (stepIndex < stepNames.length - 1) {
      targetStepName = stepNames[stepIndex + 1]
    } else {
      const groupNames = Object.keys(this._flow)
      const groupIndex = groupNames.indexOf(currentGroupName)

      if (groupIndex < groupNames.length - 1) {
        targetGroupName = groupNames[groupIndex + 1]
        targetStepName = Object.keys(this._flow[targetGroupName])[0]
      }
    }

    if (!targetStepName) return ''

    // Handle conditional jumps (function-based navigation)
    return this.tryHandleConditionalJumps(
      targetGroupName,
      targetStepName,
      document,
      options,
    )
  }

  findSkipStep(path: string, document: TDocument, options: TOptions): string {
    const [currentGroupName, currentStepName] = path.split('.')
    const currentStep = this._flow[currentGroupName][currentStepName]
    console.log('=>(FlowController_v1.ts:124) currentStep', currentStep)

    // if current step has skipPath defined, use it as target
    if (
      typeof currentStep === 'object' &&
      currentStep !== null &&
      'skipPath' in currentStep
    ) {
      const skipPath = currentStep.skipPath
      console.log('=>(FlowController_v1.ts:133) skipPath', skipPath)

      if (skipPath) {
        const [targetGroupName, targetStepName] = skipPath.split('.')

        // Handle conditional jumps for the skip target
        return this.tryHandleConditionalJumps(
          targetGroupName,
          targetStepName,
          document,
          options,
        )
      }
    }

    // If no skipPath defined, fall back to normal next step behavior
    return this.findNextStep(path, document, options)
  }

  getNextStep(
    currentStep: string,
    document: TDocument,
    options: TOptions,
  ): string {
    const nextStep = this.findNextStep(currentStep, document, options)
    console.log('=>(FlowController_v1.ts:137) nextStep', nextStep)
    return nextStep
  }

  getPreviousStep(
    currentStep: string,
    document: TDocument,
    options: TOptions,
  ): string {
    // We cannot just get a previous step because it can be conditional step pointing to some other step (or current, causing infinite loop)
    // Also we could go to the current step by conditional jump from the very beginning of the flow, so we cannot get previous non-conditional step
    // So here we get all steps in the flow with regards to conditional jumps, and just take precalculated previous one
    const flowSteps = this.getFlowSteps(undefined, true, document, options)
    console.log('=>(FlowController_v1.ts:171) flowSteps', flowSteps)
    const currentStepIndex = flowSteps.indexOf(currentStep)

    if (currentStepIndex > 0) {
      return flowSteps[currentStepIndex - 1]
    } else {
      // If we're at the first step, return current step
      return currentStep
    }
  }

  private tryHandleConditionalJumps(
    targetGroup: string,
    targetStep: string,
    document: TDocument,
    options: TOptions,
  ): string {
    const step = this._flow[targetGroup][targetStep]

    if (typeof step === 'function') {
      const gotoPath = step(document, options)
      const [groupName, stepName] = gotoPath.split('.')

      return this.tryHandleConditionalJumps(
        groupName,
        stepName,
        document,
        options,
      )
    } else {
      return `${targetGroup}.${targetStep}`
    }
  }
}
