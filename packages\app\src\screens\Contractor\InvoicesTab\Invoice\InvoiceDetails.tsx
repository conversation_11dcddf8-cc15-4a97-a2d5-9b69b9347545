import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { StyleSheet, Text, View } from 'react-native'
import { useFocusEffect } from '@react-navigation/native'
import { IconInvoicePrompt } from '../../../../assets/icons'
import {
  dictionaries,
  IInvoiceModel,
  InvoicePaymentType,
  routes,
} from '@linqpal/models'
import { theme } from '@linqpal/common-frontend'
import { Divider } from 'react-native-paper'
import Loading from '../../../Loading'
import { Link } from '../../../../ui/atoms/Link'
import { ConfirmPrompt } from '../../../../ui/molecules/ConfirmPrompt'
import BuilderInvoiceSignup from '../BuilderInvoiceSignup'
import RootStore, { useStore } from '../../../../store'
import { WhatIsBTModal } from '../WhatIsBTModal'
import { ApplyBTCreditItem } from '../../../../ui/molecules/ApplyBTCreditItem'
import { Spacer } from '../../../../ui/atoms'
import { isWeb } from '../../../../utils/helpers/commonUtils'
import cookies from '../../../../utils/service/cooker'
import BuilderBottomModal from '../../../../ui/molecules/BuilderBottomModal'
import { useInvoiceDetailsContext } from '../../TabInvoice/InvoiceDetailsContext'
import { InvoicePaymentStatusModal } from '../../../../ui/organisms/InvoicePaymentStatusModal'
import { AddToProjectModal } from '../../../../ui/organisms/AddToProjectModal'
import { ProjectSelectionBottomModal } from '../../../../ui/organisms/ProjectSelectionBottomModal'
import {
  AddBankAccountMethodBottomModal,
  AddPaymentMethodItems,
} from '../../../../ui/add-payment-method-components'
import { observer } from 'mobx-react-lite'
import { dispatcher, pathFactory, paths } from '../../../links'
import { InvoiceAlert } from '../InvoiceAlert'
import { useTranslation } from 'react-i18next'
import SignInStore from '../../../Auth/stores/SignInStore'
import SignupStore from '../../../Auth/stores/SignupStore'
import useRecaptcha from '../../../Auth/useRecaptcha'
import { ReasonModal } from '../DismissReasonModal'
import LocalStorage from '../../../../store/LocalStorage'
import Wrapper from '../../Wrapper'
import {
  APPROVED_STATUSES,
  invoiceStatus,
  LOAN_APPLICATION_STATUS,
  notificationTypes,
  PROCESSING_STATUSES,
} from '@linqpal/models/src/dictionaries'
import AddPaymentMethodStore from '../../../../store/AddPaymentMethodStore'
import { PayInvoiceModal } from '../PayInvoice/PayInvoiceModal'
import CompanyHeader from './CompanyHeader'
import { OtherInvoiceInfo } from './OtherInvoiceInfo'
import {
  AddPaymentMethodOptions,
  EligibleForACHdiscount,
  InvoicesHeader,
  OrDivider,
  PayNowButton,
} from './components'
import { SelectPurchaseType } from '../SelectPurchaseType'
import {
  AddProjectSuccessModal,
  PaymentProcessingAlert,
  UploadedInvoicePaymentSuccessAlert,
} from './modals'
import { ConnectBank as ConnectCreditApplicationBankWithPlaid } from '../ConnectBankWithPlaid/ConnectBank'
import ApplicationStore from '../../../GeneralApplication/Application/ApplicationStore'
import { getSnapshot } from 'mobx-state-tree'
import { PaymentMethodsList } from './components/PaymentMethodsList'
import useIsMobile from '../../PayablesTab/hooks/useIsMobile'
import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'
import { InvoiceCreditPaymentFlow } from '../../InvoicePayment/Credit/InvoiceCreditPaymentFlow'

const { commonColors } = theme

interface InvoiceDetailsProps {
  navigation: any
  route: any
}

export interface Invoice extends IInvoiceModel {
  _id: string
  existingCustomer?: any
  company: any
  bankAccounts: any
  customer: any
  isPaymentMethodsAdded: boolean
  project?: any
  achDiscount: number
  lateFee: number
}

export interface PaymentMethod {
  id: string
  paymentMethodType: string
  name: string
  accountNumber: string
  accountType: string
  cardMetadata?: any
}

export interface LoanAppDetails {
  status: string
  currentInvoiceCreditStatus?: string
}

export const AddCreditCardComponent = ({ navigation, sourceInvoice }) => {
  useEffect(() => {
    navigation.navigate(paths.LinkCard, { id: sourceInvoice })
  }, [navigation, sourceInvoice])

  return <View />
}

export const InvoiceDetails = observer(
  ({ navigation, route }: InvoiceDetailsProps) => {
    const { t } = useTranslation('global')
    const {
      userStore,
      screensStore: { notificationStore },
      remoteConfig,
    } = useStore()
    const {
      screensStore: { paymentMethodsStore },
    } = RootStore
    const [invoice, setInvoice] = useState<Invoice | null>(null)
    const [loading, setLoading] = useState(true)
    const [loanAppDetails, setLoanAppDetails] = useState<LoanAppDetails>({
      status: '',
      currentInvoiceCreditStatus: '',
    })
    const [cancelModal, setCancelModal] = useState(false)
    const [whatIsModal, setWhatIsModal] = useState(false)
    const [btcredit, setBtcredit] = useState(false)
    const [reasonModal, setReasonModal] = useState(false)
    const [bnplAvailable, setBnplAvailable] = useState(false)
    const [cardPaymentAvailable, setCardPaymentAvailable] = useState(false)
    const [acceptAchPayment, setAcceptAchPayment] = useState(false)
    const [areRegularPaymentsDisabled, setAreRegularPaymentsDisabled] =
      useState(false)
    const [showInvoiceAlert, setShowInvoiceAlert] = useState(false)
    const [payNow, setPayNow] = useState(false)
    const [isFactoring, setIsFactoring] = useState<boolean | null>(null)
    const isUserAuthenticated = isWeb
      ? !!cookies.get('session')
      : userStore.isAuthenticated
    const {
      payInvoiceModal,
      setPayInvoiceModal,
      invoicePaymentMethod,
      setInvoicePaymentMethod,
    } = useInvoiceDetailsContext()
    const displayAlertStatuses = useMemo(() => {
      return [
        invoiceStatus.cancelled,
        invoiceStatus.dismissed,
        invoiceStatus.expired,
        invoiceStatus.paymentPosted,
        invoiceStatus.paymentProcessing,
        invoiceStatus.paid,
      ]
    }, [])
    const recaptcha = useRecaptcha('signin')
    const {
      setModal,
      showAddPaymentMethod,
      isChosenMethodTypeFromInvoiceBank,
      isChosenMethodTypeFromInvoiceCard,
      setChosenMethodTypeFromInvoice,
    } = AddPaymentMethodStore

    const exists =
      !!invoice?.supplierInvitationDetails || invoice?.existingCustomer
    const builderCreatedInvoice = !!invoice?.supplierInvitationDetails
    const minBnplAmount = remoteConfig.get('de_min_approval_amount') || 5
    const id = route?.params?.id

    const isMobile = useIsMobile()

    const isInvoiceEligible = useMemo(() => {
      return (
        bnplAvailable &&
        loanAppDetails?.status !== LOAN_APPLICATION_STATUS.REJECTED &&
        loanAppDetails?.currentInvoiceCreditStatus !==
          LOAN_APPLICATION_STATUS.REJECTED
      )
    }, [bnplAvailable, loanAppDetails])

    const visible = useMemo(() => {
      return (
        isUserAuthenticated ||
        SignInStore.showVerifyPage ||
        SignupStore.isValidLogin ||
        (!exists && (invoice?.customer?.phone || invoice?.customer?.email))
      )
      // disabled because all these deps are needed
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
      isUserAuthenticated,
      SignupStore.codeVerifier,
      SignInStore.showVerifyPage,
      SignupStore.isValidLogin,
    ])

    const isQuote = useMemo(() => {
      return invoice?.type === 'quote'
    }, [invoice?.type])

    const isBTCreditDisabled =
      !!PROCESSING_STATUSES.find(
        (s) => s === loanAppDetails.currentInvoiceCreditStatus,
      ) ||
      !!APPROVED_STATUSES.find(
        (s) => s === loanAppDetails.currentInvoiceCreditStatus,
      )

    const showOtherPaymentMethods = isUserAuthenticated
    const canAddPaymentMethod = !invoice?.isPaymentMethodsAdded
    const canUsePaymentMethods = canAddPaymentMethod || showOtherPaymentMethods

    {
      /* ================================================== Functions ============================================ */
    }

    const getBankAccounts = useCallback(() => {
      RootStore.screensStore.paymentMethodsStore.fetchPaymentMethods()
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    const showModal = () => {
      setPayInvoiceModal(true)
      setInvoicePaymentMethod(paymentMethodsStore.paymentMethods[0] || true)
    }

    const onPaymentSelection = (method) => {
      if (isQuote) return // disallow non-credit payments for quotes phase #1

      if (method.paymentMethodType === 'card' && !cardPaymentAvailable) {
        setShowInvoiceAlert(true)
      } else {
        // mobx and reducers conflict each other, use snapshot around mobx proxies as workaround
        setInvoicePaymentMethod(getSnapshot(method))
        setPayInvoiceModal(true)
      }
    }

    const setupAuthentication = () => {
      if (!exists) {
        SignupStore.setUpInvoiceLinkSignup({
          ...invoice?.customer,
          invoiceId: invoice?._id,
        })
      }
      if (SignInStore.isValidPhone && !isUserAuthenticated) {
        SignInStore.removeError('verify')
        SignInStore.signIn(recaptcha.verifier)
      } else if (SignInStore.isValidEmail && !isUserAuthenticated) {
        SignInStore.setCanAskPassword(true)
      }
    }

    const onPayNow = () => {
      setupAuthentication()
      setPayNow(true)
    }
    const addBankOnNavigate = useCallback(() => {
      setModal('')
      getBankAccounts()
    }, [setModal, getBankAccounts])

    const onNavigate = () => {
      setupAuthentication()
    }

    {
      /* ================================================================================================= */
    }

    useFocusEffect(
      useCallback(() => {
        if (id) {
          setChosenMethodTypeFromInvoice('')
          setLoading(true)
          routes.invoices
            .show({ id })
            .then((resp) => {
              if (resp.invoices) {
                const inv = resp.invoices[0]
                setInvoice(inv)
                setAreRegularPaymentsDisabled(
                  (!inv.company_id && inv.supplierInvitationDetails) ||
                    inv.total_amount < 0,
                )
                SignInStore.setLogin(inv?.existingCustomer?.login)
                setLoanAppDetails({
                  status: resp?.loanApplicationStatus || 'new',
                  currentInvoiceCreditStatus: resp?.currentInvoiceCreditStatus,
                })
                setBnplAvailable(resp.bnplPaymentAvailable)
                setCardPaymentAvailable(resp.cardPaymentAvailable)
                setAcceptAchPayment(resp.acceptAchPayment)

                const isFactoringInvoice =
                  inv.paymentType === InvoicePaymentType.FACTORING ||
                  inv.customer?.type === CustomerAccountType.IHC
                setIsFactoring(isFactoringInvoice)
                setShowInvoiceAlert(displayAlertStatuses.includes(inv.status))
              }
            })
            .catch((err) => {
              console.log(err.message)
            })
            .finally(() => {
              setLoading(false)
            })
        }
      }, [id, displayAlertStatuses, setChosenMethodTypeFromInvoice]),
    )

    useEffect(() => {
      isUserAuthenticated && getBankAccounts()
    }, [isUserAuthenticated, getBankAccounts])

    useEffect(
      () => ApplicationStore.getPaymentPlans(id, invoice?.total_amount),
      [id, invoice],
    )

    useEffect(() => {
      isUserAuthenticated &&
        invoice &&
        invoice._id === id &&
        routes.notification
          .markAsViewed({
            key: 'metadata.invoice_id',
            value: id,
            types: [notificationTypes.invoice, notificationTypes.quotePlaced],
            alertTypes: ['due', 'expiring', 'sent'],
          })
          .then(() => notificationStore.fetchNotifications())
          .catch((err) => console.log(err))
    }, [id, isUserAuthenticated, invoice, notificationStore])

    useEffect(() => {
      if (exists) {
        recaptcha.createVerifier()
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [SignInStore.showVerifyPage, exists])

    const { showReasonModal = false } = route?.params || {}
    useEffect(() => {
      showReasonModal && setReasonModal(showReasonModal)
    }, [showReasonModal])

    if (loading) {
      return <Loading />
    }

    if (!invoice || typeof isFactoring !== 'boolean') {
      return <></>
    }

    return (
      <Wrapper
        containerStyle={{ alignSelf: 'center' }}
        contentContainerStyle={{ height: '100%' }}
        toolbar={
          <InvoicesHeader
            inv={invoice}
            navigation={navigation}
            isUserAuthenticated={isUserAuthenticated}
          />
        }
      >
        <CompanyHeader
          company={
            builderCreatedInvoice
              ? invoice.supplierInvitationDetails
              : invoice.company
          }
          totalAmount={invoice.total_amount + invoice.lateFee}
          amountLabel={t('TabInvoice.amount_due')}
          style={{
            paddingVertical: 20,
            flexDirection: 'row',
          }}
        />
        <Divider style={{ marginBottom: 14 }} />
        <OtherInvoiceInfo invoice={invoice} />
        {canUsePaymentMethods &&
          (isQuote ? (
            <Spacer height={34} />
          ) : (
            <Text style={styles.chooseMethodText}>
              {t('InvoiceDetails.choose-method')}
            </Text>
          ))}
        {isInvoiceEligible &&
          !!ApplicationStore.paymentPlans.length &&
          canUsePaymentMethods &&
          !isFactoring && (
            <>
              <ApplyBTCreditItem
                loanAppDetails={loanAppDetails}
                amountLessThanMin={invoice?.total_amount < minBnplAmount}
                onPress={() => {
                  setupAuthentication()
                  setBtcredit(true)
                }}
                navigation={navigation}
                setModal={setBtcredit}
                showInfo
                disabled={isBTCreditDisabled}
              />
              <Spacer height={23} />
              {!isBTCreditDisabled && !areRegularPaymentsDisabled && (
                <OrDivider showText={!isQuote} />
              )}
            </>
          )}

        {!areRegularPaymentsDisabled && !isBTCreditDisabled && !isQuote && (
          <>
            {acceptAchPayment && !!invoice.achDiscount && (
              <EligibleForACHdiscount
                label={t('InvoiceDetails.InvoiceEligibleForACHdiscount')}
              />
            )}

            {canAddPaymentMethod ? (
              <AddPaymentMethodOptions
                navigation={navigation}
                id={id}
                onNavigate={onNavigate}
                getBankAccounts={getBankAccounts}
                canAddBankAccount={acceptAchPayment}
              />
            ) : showOtherPaymentMethods ? (
              <PaymentMethodsList
                acceptAchPayment={acceptAchPayment}
                invoice={invoice}
                onSelection={onPaymentSelection}
              />
            ) : (
              <PayNowButton invoice={invoice} onPayNow={onPayNow} />
            )}
          </>
        )}

        {isQuote && !isUserAuthenticated && (
          <>
            <PayNowButton invoice={invoice} onPayNow={onPayNow} />
            <Spacer height={23} />
          </>
        )}

        {/* Option hidden until a supplier switch for this is developed
        <Link
          title={t('InvoiceDetails.i-dont-want-to-pay')}
          color="#DA515E"
          textStyle={{ fontSize: 14, marginVertical: 26 }}
          onPress={() => {
            setCancelModal(true)
          }}
        /> */}
        {!exists && (
          <Link
            title={t('InvoiceDetails.what-is-bt')}
            color={commonColors.accentText}
            textStyle={{ fontSize: 14 }}
            onPress={() => {
              setWhatIsModal(true)
            }}
          />
        )}

        {/*  ====================================================== MODALS ========================================================  */}

        <InvoiceAlert
          visible={showInvoiceAlert}
          statusData={{
            invoiceStatus: invoice?.status,
            cardPaymentAvailable,
          }}
          invoice={invoice}
          customLabel={
            displayAlertStatuses.includes(invoice?.status as any)
              ? null
              : 'Choose payment'
          }
          onClose={() => {
            setShowInvoiceAlert(false)
            if (displayAlertStatuses.includes(invoice?.status as any)) {
              setInvoice(null)
              if (isUserAuthenticated) {
                navigation.navigate(paths.Console.Payables.home)
              } else {
                const action = dispatcher(pathFactory('Auth.SignIn'))
                navigation.replace(action.name, action.params)
              }
            }
          }}
        />

        <ReasonModal
          modal={reasonModal}
          setModal={setReasonModal}
          invoices={invoice}
          navigation={navigation}
        />

        {!![
          dictionaries.LOAN_APPLICATION_STATUS.APPROVED,
          dictionaries.LOAN_APPLICATION_STATUS.PROCESSING,
          dictionaries.LOAN_APPLICATION_STATUS.CANCELED,
        ].find((s) => s === loanAppDetails?.status) && (
          <InvoiceCreditPaymentFlow
            invoices={[invoice]}
            navigation={navigation}
            onClose={() => setBtcredit(false)}
          />
        )}

        <AddToProjectModal invoice={invoice} />

        <ProjectSelectionBottomModal invoice={invoice} />

        <WhatIsBTModal
          modal={whatIsModal}
          setModal={setWhatIsModal}
          setBtcredit={setBtcredit}
          isInvoiceEligible={isInvoiceEligible}
        />

        <PaymentProcessingAlert navigation={navigation} />

        <UploadedInvoicePaymentSuccessAlert navigation={navigation} />

        <InvoicePaymentStatusModal
          invoice={invoice}
          totalAmount={
            Number(invoice?.total_amount || 0) - (invoice?.achDiscount || 0)
          }
        />

        <AddProjectSuccessModal
          navigation={navigation}
          companyName={invoice?.company?.name}
          builderCreatedInvoice={builderCreatedInvoice}
        />

        <BuilderInvoiceSignup
          invoice={invoice}
          visible={isChosenMethodTypeFromInvoiceBank && visible}
          onClose={() => {
            setModal('')
          }}
          navigation={navigation}
          route={route}
        >
          <AddBankAccountMethodBottomModal
            sourceInvoice={id}
            onNavigate={addBankOnNavigate}
          />
        </BuilderInvoiceSignup>

        <BuilderInvoiceSignup
          invoice={invoice}
          visible={isChosenMethodTypeFromInvoiceCard && visible}
          onClose={() => setChosenMethodTypeFromInvoice('')}
          navigation={navigation}
          route={route}
        >
          {!!isChosenMethodTypeFromInvoiceCard && (
            <AddCreditCardComponent
              navigation={navigation}
              sourceInvoice={id}
            />
          )}
        </BuilderInvoiceSignup>

        <BuilderInvoiceSignup
          invoice={invoice}
          visible={payInvoiceModal}
          onClose={() => {
            setPayInvoiceModal(false)
            setInvoicePaymentMethod({})
          }}
          navigation={navigation}
          route={route}
        >
          {Object.keys(invoicePaymentMethod).length ? (
            <PayInvoiceModal
              modal={payInvoiceModal}
              setModal={() => {
                setPayInvoiceModal(false)
                setInvoicePaymentMethod({})
              }}
              paymentPlan={null}
              account={invoicePaymentMethod}
              invoices={[invoice]}
              navigation={navigation}
              showModal={showModal}
            />
          ) : (
            <></>
          )}
        </BuilderInvoiceSignup>

        <BuilderInvoiceSignup
          invoice={invoice}
          visible={payNow && visible}
          onClose={() => {
            setPayNow(false)
          }}
          navigation={navigation}
          route={route}
        />

        <BuilderInvoiceSignup
          invoice={invoice}
          btcredit
          visible={btcredit && visible}
          onClose={() => {
            setBtcredit(false)
          }}
          setLoading={setLoading}
          navigation={navigation}
          route={route}
          status={loanAppDetails?.status}
        />

        {!isMobile && (
          <BuilderInvoiceSignup
            invoice={invoice}
            visible={showAddPaymentMethod}
            onClose={() => {
              setModal('')
            }}
            navigation={navigation}
            route={route}
          >
            <BuilderBottomModal
              height={acceptAchPayment ? 300 : 200}
              visible={showAddPaymentMethod}
              onClose={() => setModal('')}
            >
              <AddPaymentMethodItems
                sourceInvoice={id}
                navigation={navigation}
                canAddBankAccount={acceptAchPayment}
                goToNextOnboardingPage={getBankAccounts}
                itemStyle={{ otherStyles: { title: { lineHeight: 24 } } }}
              />
            </BuilderBottomModal>
          </BuilderInvoiceSignup>
        )}

        <ConnectCreditApplicationBankWithPlaid
          recheckConditionsBeforeApplyingCredit={() => setBtcredit(true)}
        />

        <ConfirmPrompt
          visible={cancelModal}
          icon={<IconInvoicePrompt />}
          onDismiss={() => {
            setCancelModal(false)
          }}
          title={t('DismissAlert.title')}
          message={t('DismissAlert.message')}
          firstButton={{
            title: 'Yes, dismiss',
            mode: 'contained',
            bgColor: '#E6F2FA',
            textColor: commonColors.accentText,
            onPress: () => {
              setCancelModal(false)
              if (userStore.user) {
                setReasonModal(true)
              } else {
                if (invoice?.existingCustomer) {
                  SignInStore.setLogin(invoice.existingCustomer.login)
                }
                LocalStorage.set('screen', {
                  screen: 'Console.Payables.Vendor',
                  params: { id: invoice._id, showReasonModal: true },
                }).then(() => {
                  const action = dispatcher(pathFactory('Auth.SignIn'))
                  navigation.replace(action.name, action.params)
                })
              }
            },
          }}
          secondButton={{
            title: "I'll pay later",
            mode: 'contained',
            bgColor: commonColors.accentText,
            onPress: () => setCancelModal(false),
          }}
        />
        <SelectPurchaseType />
      </Wrapper>
    )
  },
)

const styles = StyleSheet.create({
  chooseMethodText: {
    fontSize: 16,
    color: '#003353',
    fontWeight: '600',
    marginTop: 34,
    marginBottom: 16,
    lineHeight: 21,
    alignSelf: 'center',
  },
})
