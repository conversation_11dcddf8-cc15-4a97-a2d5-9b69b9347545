import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react'

import { ScrollView, View } from 'react-native'

import { CompanyUtils } from '@linqpal/models/src/helpers/companyUtils'
import RootStore from '../../../../store/RootStore'
import ApplicationStore from '../../../GeneralApplication/Application/ApplicationStore'
import { useResponsive } from '../../../../utils/hooks'
import { FlowController } from '../../../GeneralApplication/Application/FlowController'
import { getGroupTitle } from '../../../GeneralApplication/Application/groupTitles'
import { WizardHeader } from './WizardHeader'
import {
  CloseApplicationAlert,
  MobileButtons,
} from '../../../GeneralApplication/Application/components'
import { Spacer } from '../../../../ui/atoms'
import { WizardGroupTitle } from './WizardGroupTitle'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import {
  getApplicationStepEditor,
  IApplicationStepDescription,
  IApplicationStepEditor,
  IApplicationStepEditorTitle,
} from '../../flow/ApplicationEditorSelector'
import { WizardStepTitle } from './WizardStepTitle'
import { WizardStepDescription } from './WizardStepDescription'
import { WizardDesktopButtons } from './WizardDesktopButtons'
import { WizardBackButton } from './WIzardBackButton'

export default observer(function WizardStepEditor() {
  const { t } = useTranslation('application')
  const [displayCloseAlert, setDisplayCloseAlert] = useState(false)
  const [showNavigationControls, setShowNavigationControls] = useState(true)
  const { isBusy, userStore } = RootStore
  const { isOptionalStep } = ApplicationStore
  const { screenWidth, sm } = useResponsive()

  const document = RootStore.userStore?.document

  const flowController = useMemo(() => new FlowController(document), [document])

  const currentPath = flowController.getCurrentStep(document)

  const [groupName, itemName] = currentPath.split('.')

  const {
    canSkip = true,
    canMoveNext = true,
    showFooterMessage,
    handleBack,
  } = flowController.findComponentByPath(currentPath) || {}

  // useEffect(() => {
  //   const flowSteps = flowController.getFlowSteps(true)
  //
  //   if (!flowSteps.includes(currentPath)) {
  //     document.setCurrent('review.review')
  //   }
  // }, [currentPath, document, flowController])

  // useEffect(() => {
  //   if (document?.current === 'review.agreement') {
  //     document.setCurrent('review.review')
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [])

  const [currentValue, setCurrentValue] = useState<any>({
    value: '',
    filled: false,
    group: groupName,
    identifier: itemName,
  })

  const onValueUpdate = useCallback((updateInfo: any) => {
    setCurrentValue(updateInfo)
  }, [])

  const onSetNavigationVisible = useCallback((visible: boolean) => {
    setShowNavigationControls(visible)
  }, [])

  const onNext = () =>
    canMoveNext &&
    flowController.moveToNextStep(
      currentValue,
      getGroupTitle(currentValue.group),
    )

  const handleSkip = () =>
    canSkip &&
    flowController.skipToNextStep(
      currentValue,
      getGroupTitle(currentValue.group),
    )

  const onCloseApplication = () => {
    setDisplayCloseAlert(false)

    flowController.updateDocument(
      currentValue,
      getGroupTitle(currentValue.group),
    )

    ApplicationStore.setCurrentCoOwnerIndex(-1)
  }

  const flowSteps = flowController.getFlowSteps()
  const canSubmit = ApplicationStore.canSubmit(flowSteps)

  const flowStep = RootStore.userStore?.document?.current
  const isPreview = flowStep === 'review.preview'
  const isReview = flowStep === 'review.review'

  const screenTitle = ApplicationStore.isGetPaidApplication
    ? t('GetPaidApplication')
    : ApplicationStore.isCreditApplication
    ? t('CreditRequest')
    : ApplicationStore.isInHouseCreditApplication
    ? t('InHouseCreditApplication', {
        supplierName: CompanyUtils.getCompanyName(
          userStore.suppliers?.find(
            (s) => s.id === ApplicationStore.supplierId,
          ),
        ),
      })
    : ''

  /// ^^^-- old version
  /// vvv-- new version
  // TODO: VK: Unified: hide title for review.agreement
  const store = useUnifiedApplication()

  const [showTitle /*, setShowTitle*/] = useState(true)

  const scrollRef = useRef<any>()

  useEffect(() => {
    // reset scroll position when navigating between large components
    // like preview -> coowner -> preview
    scrollRef.current?.scrollTo({ y: 0, animated: false })
  }, [store.currentStep])

  // TODO: VK: Unified: review, replace isOptionalStep with field-specific validation
  const disableNext = !store.isCurrentStepValid && !isOptionalStep(flowStep)

  const editor: IApplicationStepEditor = useMemo(
    () =>
      store.currentStep
        ? getApplicationStepEditor(store.currentStep)
        : { component: () => <></> },
    [store.currentStep],
  )

  const onBack = () => {
    // let the current component do its own back handling
    // and do backward navigation only when needed
    if (!handleBack || !handleBack()) {
      console.log('here')
      setShowNavigationControls(true)
      store.moveBackward()
    }
  }

  console.log('wizard step', {
    currentGroup: store.currentGroup,
    currentStep: store.currentStep,
    isValid: store.isCurrentStepValid,
    editor,
    draft: store.document,
  })

  return (
    <View style={{ flex: 1 }}>
      <WizardHeader
        title={screenTitle}
        canSkip={editor.canSkip && showNavigationControls}
        onClose={() => setDisplayCloseAlert(true)}
        onSkip={handleSkip}
      />

      {showTitle && <WizardGroupTitle />}

      <ScrollView
        ref={scrollRef}
        contentContainerStyle={{ flexGrow: 1, alignItems: 'center' }}
        showsVerticalScrollIndicator={false}
      >
        <View
          style={{
            borderColor: 'red',
            borderWidth: 1,
            paddingHorizontal: sm ? 0 : 20,
            paddingVertical: isPreview ? 0 : 20,
            flex: 1,
            minWidth: screenWidth > 700 ? 700 : '100%',
            maxWidth: screenWidth > 700 ? 700 : undefined,
          }}
        >
          {sm && flowController.canMoveToPreviousStep() && (
            <WizardBackButton onBack={onBack} />
          )}

          <Spacer height={sm ? 20 : 10} />

          {/* TODO: VK: Unified: remove cast to any, it's just for backward compatibility during refactoring  */}
          <WizardStepTitle
            title={editor.title as IApplicationStepEditorTitle}
          />

          {/* TODO: VK: Unified: remove cast to any, it's just for backward compatibility during refactoring  */}
          <WizardStepDescription
            description={editor.description as IApplicationStepDescription}
          />

          {editor.component ? (
            <editor.component
              onValueUpdate={onValueUpdate}
              flowController={flowController}
              setNavigationVisible={
                onSetNavigationVisible
              } /* TODO: VK: replace with onSetEditorOptions({ showNavigation, showTitle etc. }) */
            />
          ) : null}

          {sm && showNavigationControls ? (
            <WizardDesktopButtons canSkip={editor.canSkip} />
          ) : null}
        </View>

        {!sm && showNavigationControls && (
          <MobileButtons
            t={t}
            sm={sm}
            next={canMoveNext}
            onNext={onNext}
            onBack={onBack}
            document={document}
            currentItem={itemName}
            disableNext={disableNext}
            isBusy={isBusy}
            canSubmit={canSubmit}
            showFooterMessage={showFooterMessage}
            isReview={isReview}
          />
        )}
      </ScrollView>

      {displayCloseAlert && (
        <CloseApplicationAlert
          onCloseAlert={() => setDisplayCloseAlert(false)}
          onCloseApplication={onCloseApplication}
        />
      )}
    </View>
  )
})

// const styles = StyleSheet.create({
//   contentWrapper: {},
// })
