import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { ActivityIndicator } from 'react-native-paper'
import { SButton } from '../../../../../ui/atoms'
import { BtAlert, BtText } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import RootStore from '../../../../../store'
import { styles } from '../../AccountingSystem/styles'
import { useTranslation } from 'react-i18next'
import {
  MESSAGE_TYPE,
  useMessage,
} from '../../../../../utils/helpers/MessageProvider'
import { useResponsive } from '../../../../../utils/hooks'
import { QuickbooksImportCompletedPopup } from '../components/QuickBooks/QuickBooksImportCompletedPopup'
import {
  ConnectIntegrationFlowType,
  getIntegrationConfig,
} from '../integrationsConfig'
import { IQuickBooksSettings, IZohoBooksSettings } from '@linqpal/models'
import { useObserver } from 'mobx-react-lite'
import { ServiceSettingsModal } from './ServiceSettingsModal'

export enum Status {
  Unknown,
  Connected,
  Disconnected,
}

export const ConnectButton = observer(
  ({
    serviceName,
    route,
    connectionDisabled = true,
    setConnectionChecked,
  }: {
    serviceName: string
    route: any
    connectionDisabled?: boolean
    setConnectionChecked: React.Dispatch<React.SetStateAction<number>>
  }) => {
    const { sm } = useResponsive()
    const { t } = useTranslation('global')
    const { userStore } = RootStore
    const integrationConfig = getIntegrationConfig(t, userStore)
    const config = integrationConfig[serviceName]
    const { route: r } = route

    const [loading, setLoading] = useState(false)

    const connectionInProgress = useRef(false)
    const [, setState] = useState({})
    const forceUpdate = () => setState({})

    const [settings, setSettings] = useState<
      IZohoBooksSettings | IQuickBooksSettings | null
    >(null)
    const [showConnectConfirmation, setShowConnectConfirmation] =
      useState(false)
    const [warning, setWarning] = useState<string>()
    const [connectionAttempted, setConnectionAttempted] = useState<
      Record<string, boolean>
    >({})

    const windowName = 'popup'

    const connectionStatus = useObserver(
      () => userStore.connectedServices[config.name],
    )

    const ConfirmationComponent =
      config.confirmationComponent as React.ComponentType<{
        visible: boolean
        onConnect: () => void
        onCancel: () => void
      }>

    const status = useMemo(
      () =>
        connectionStatus === null || connectionStatus === undefined
          ? Status.Unknown
          : connectionStatus
          ? Status.Connected
          : Status.Disconnected,
      [connectionStatus],
    )

    const { setMessage } = useMessage()

    const realmId = useRef(null)

    const isCallbackRequest = useCallback(
      () => config.isCallbackRequest(r?.params),
      [config, r?.params],
    )

    //child (popup) window handling
    useEffect(() => {
      if (!window.name.startsWith(windowName)) return
      if (!window.opener) return
      if (!r?.params || !isCallbackRequest()) return
      if (config.flowType !== ConnectIntegrationFlowType.OAUTH) return

      const params = new URLSearchParams({
        code: r?.params?.code ?? '',
        realmId: r?.params?.realmId ?? '',
      })
      window.opener.location.href = `${
        window.location.href
      }settings?${params.toString()}`
      window.close()
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [r?.params, isCallbackRequest])

    //parent (opener) window handling
    useEffect(() => {
      const shouldSkipCallback =
        window.name.startsWith(windowName) ||
        window.opener ||
        connectionInProgress.current ||
        connectionAttempted[serviceName] ||
        !r?.params ||
        !isCallbackRequest() ||
        status === Status.Connected ||
        connectionStatus === null ||
        connectionStatus === undefined ||
        config.flowType !== ConnectIntegrationFlowType.OAUTH

      if (shouldSkipCallback) return

      const handleCallback = async () => {
        connectionInProgress.current = true
        // after QuickBooks 2FA window name is appended with some ID
        try {
          const connectParams = {
            code: r?.params?.code,
            realmId: r?.params?.realmId,
            type: serviceName,
          }
          realmId.current = connectParams.realmId

          try {
            const resp = await config.connect(connectParams)
            const isConnected = Boolean(resp?.result)

            config.setConnectionStatus(isConnected)
          } catch (e) {
            config.setConnectionStatus(false)

            if (e.code === 'integration_exists') {
              setWarning(config.labels.integrationExists)
            } else {
              setMessage(MESSAGE_TYPE.ERROR, config.labels.connectionFailed)
              console.error(e)
            }
          } finally {
            setConnectionAttempted((prev) => ({
              ...prev,
              [serviceName]: true,
            }))
            // Remove the query params from the URL
            const url = window.location.href.split('?')[0]
            window.history.replaceState({}, document.title, url)
          }
        } finally {
          connectionInProgress.current = false
          forceUpdate()
        }
      }

      handleCallback()

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [r?.params, serviceName, isCallbackRequest])

    const onConnectAttempt = () => {
      setShowConnectConfirmation(true)
    }

    const onCancelConnectAttempt = () => {
      setShowConnectConfirmation(false)
    }

    const onConnectOauth = () => {
      setShowConnectConfirmation(false)
      setLoading(true)

      config
        .authorize()
        .then((res: any) => {
          const popup = window.open(
            res.result,
            windowName,
            'height=900,width=800',
          )
          if (popup !== null) {
            const timer = setInterval(() => {
              if (popup?.closed) {
                clearInterval(timer)
                setLoading(false)
              }
            }, 500)
          }
        })
        .catch(() => {
          setMessage(MESSAGE_TYPE.ERROR, config.labels.connectionFailed)
        })
        .finally(() => {
          setLoading(false)
        })
    }

    const onDisconnect = () => {
      setLoading(true)

      config
        .disconnect()
        .then(() => {
          config.setConnectionStatus(false)
          setConnectionChecked((prevValue: number) => prevValue + 1)
        })
        .catch(() => {
          setMessage(MESSAGE_TYPE.ERROR, config.labels.disconnectionFailed)
        })
        .finally(() => setLoading(false))
    }

    const onShowSettings = () => {
      setLoading(true)

      config
        .showSettings()
        .then((res) => {
          setLoading(false)
          setSettings(res.result)
        })
        .catch(() => {
          setMessage(MESSAGE_TYPE.ERROR, config.labels.showSettingsFailed)
        })
        .finally(() => setLoading(false))
    }

    const onConnectModalSettings = () => {
      setLoading(true)

      config
        .showSettings()
        .then((res) => {
          setLoading(false)
          setSettings(res.result)
        })
        .catch(() => {
          setMessage(MESSAGE_TYPE.ERROR, config.labels.connectionFailed)
        })
        .finally(() => setLoading(false))
    }

    const onSettingsClose = () => {
      setSettings(null)
    }

    const onWarningClose = () => {
      setWarning(undefined)
    }

    if (status === Status.Unknown || loading || connectionInProgress.current)
      return (
        <ActivityIndicator style={styles.activityIndicator} color="#335C75" />
      )

    return (
      <>
        {status === Status.Disconnected && (
          <SButton
            onPress={
              config.flowType === ConnectIntegrationFlowType.SETTINGS_MODAL
                ? onConnectModalSettings
                : onConnectAttempt
            }
            labelStyle={[
              styles.accountingSystemButtonLabel,
              styles.connectButtonLabel,
              connectionDisabled && { color: '#99ADBA' },
            ]}
            buttonStyle={[
              styles.accountingSystemButton,
              styles.connectButton,
              connectionDisabled && {
                backgroundColor: '#DEE5EB',
              },
            ]}
            contentStyle={styles.connectButtonContent}
            disabled={
              connectionDisabled || userStore.hasAnyConnectedIntegration
            }
          >
            {config.labels.connect} {config.ui.title}
          </SButton>
        )}
        {status === Status.Connected && (
          <>
            {!!config.labels.showSettings && (
              <SButton
                onPress={onShowSettings}
                buttonStyle={[
                  styles.accountingSystemButton,
                  styles.disconnectButton,
                  { marginBottom: -10 },
                ]}
                labelStyle={[
                  styles.accountingSystemButtonLabel,
                  styles.disconnectButtonLabel,
                ]}
                contentStyle={styles.disconnectButtonContent}
              >
                {config.labels.showSettings}
              </SButton>
            )}
            {config.flowType !== ConnectIntegrationFlowType.SETTINGS_MODAL && (
              <SButton
                onPress={onDisconnect}
                buttonStyle={[
                  styles.accountingSystemButton,
                  styles.disconnectButton,
                ]}
                labelStyle={[
                  styles.accountingSystemButtonLabel,
                  styles.disconnectButtonLabel,
                ]}
                contentStyle={styles.disconnectButtonContent}
              >
                {config.labels.disconnect}
              </SButton>
            )}
          </>
        )}

        <ConfirmationComponent
          visible={showConnectConfirmation}
          onConnect={onConnectOauth}
          onCancel={onCancelConnectAttempt}
        />

        {serviceName === 'quickbooks' && <QuickbooksImportCompletedPopup />}

        <ServiceSettingsModal
          serviceName={serviceName}
          visible={!!settings}
          onClose={onSettingsClose}
          settings={settings}
          onError={() => {
            setMessage(MESSAGE_TYPE.ERROR, config.labels.updateSettingsFailed)
          }}
          onSubmit={() => {
            if (config.flowType === ConnectIntegrationFlowType.SETTINGS_MODAL) {
              config.setConnectionStatus(true)
              setConnectionChecked((prevValue: number) => prevValue + 1)
            }
          }}
        />

        <BtAlert
          visible={!!warning}
          onClose={onWarningClose}
          titleStyle={sm ? {} : { fontWeight: '600', fontSize: 20 }}
          width={sm ? 500 : 330}
          type={'warning'}
          buttons={[
            {
              label: t('close'),
              onPress: onWarningClose,
            },
          ]}
        >
          <BtText
            size={sm ? 18 : 16}
            style={{ lineHeight: 26, textAlign: 'center' }}
          >
            {warning}
          </BtText>
        </BtAlert>
      </>
    )
  },
)
